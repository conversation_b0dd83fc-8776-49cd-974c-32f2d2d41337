/* نظام طلب الواتساب المتقدم */
/* Advanced WhatsApp Order System */

class WhatsAppIntegration {
    constructor() {
        this.phoneNumber = '+966501234567'; // رقم الواتساب الخاص بك
        this.businessName = 'Tab3h'; // اسم العمل
        this.currency = 'USD'; // العملة
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateCartWhatsAppButton();
    }

    setupEventListeners() {
        // مراقبة تغييرات السلة
        document.addEventListener('cartUpdated', () => {
            this.updateCartWhatsAppButton();
        });

        // زر الواتساب في السلة
        document.addEventListener('click', (e) => {
            if (e.target.closest('.whatsapp-order-btn')) {
                e.preventDefault();
                this.sendCartOrder();
            }
        });
    }

    // إرسال طلب منتج واحد
    sendProductOrder(product) {
        const message = this.formatProductMessage(product);
        this.openWhatsApp(message);
    }

    // إرسال طلب السلة كاملة
    sendCartOrder() {
        const cart = this.getCartItems();
        if (cart.length === 0) {
            this.showNotification('السلة فارغة', 'يرجى إضافة منتجات إلى السلة أولاً', 'warning');
            return;
        }

        const message = this.formatCartMessage(cart);
        this.openWhatsApp(message);
    }

    // تنسيق رسالة منتج واحد
    formatProductMessage(product) {
        const currentLang = document.documentElement.lang || 'ar';
        
        let message = '';
        
        if (currentLang === 'ar') {
            message = `🛍️ *طلب جديد من ${this.businessName}*\n\n`;
            message += `📦 *المنتج:* ${product.name}\n`;
            message += `💰 *السعر:* ${product.price} ${this.currency}\n`;
            message += `📝 *الوصف:* ${product.description}\n\n`;
            message += `👤 *معلومات العميل:*\n`;
            message += `• الاسم: [يرجى كتابة اسمك]\n`;
            message += `• رقم الهاتف: [يرجى كتابة رقم هاتفك]\n`;
            message += `• العنوان: [يرجى كتابة عنوانك]\n\n`;
            message += `📋 *ملاحظات إضافية:* [اختياري]\n\n`;
            message += `شكراً لاختيارك ${this.businessName}! 🌟`;
        } else if (currentLang === 'en') {
            message = `🛍️ *New Order from ${this.businessName}*\n\n`;
            message += `📦 *Product:* ${product.name}\n`;
            message += `💰 *Price:* ${product.price} ${this.currency}\n`;
            message += `📝 *Description:* ${product.description}\n\n`;
            message += `👤 *Customer Information:*\n`;
            message += `• Name: [Please enter your name]\n`;
            message += `• Phone: [Please enter your phone number]\n`;
            message += `• Address: [Please enter your address]\n\n`;
            message += `📋 *Additional Notes:* [Optional]\n\n`;
            message += `Thank you for choosing ${this.businessName}! 🌟`;
        } else { // Turkish
            message = `🛍️ *${this.businessName}'den Yeni Sipariş*\n\n`;
            message += `📦 *Ürün:* ${product.name}\n`;
            message += `💰 *Fiyat:* ${product.price} ${this.currency}\n`;
            message += `📝 *Açıklama:* ${product.description}\n\n`;
            message += `👤 *Müşteri Bilgileri:*\n`;
            message += `• İsim: [Lütfen adınızı yazın]\n`;
            message += `• Telefon: [Lütfen telefon numaranızı yazın]\n`;
            message += `• Adres: [Lütfen adresinizi yazın]\n\n`;
            message += `📋 *Ek Notlar:* [İsteğe bağlı]\n\n`;
            message += `${this.businessName}'i seçtiğiniz için teşekkürler! 🌟`;
        }

        return encodeURIComponent(message);
    }

    // تنسيق رسالة السلة
    formatCartMessage(cartItems) {
        const currentLang = document.documentElement.lang || 'ar';
        let message = '';
        let total = 0;

        if (currentLang === 'ar') {
            message = `🛍️ *طلب جديد من ${this.businessName}*\n\n`;
            message += `📋 *تفاصيل الطلب:*\n`;
            message += `━━━━━━━━━━━━━━━━━━━━\n`;

            cartItems.forEach((item, index) => {
                const itemTotal = item.price * item.quantity;
                total += itemTotal;
                
                message += `${index + 1}. *${item.name}*\n`;
                message += `   💰 السعر: ${item.price} ${this.currency}\n`;
                message += `   📦 الكمية: ${item.quantity}\n`;
                message += `   💵 المجموع: ${itemTotal} ${this.currency}\n\n`;
            });

            message += `━━━━━━━━━━━━━━━━━━━━\n`;
            message += `💰 *المجموع الكلي: ${total.toFixed(2)} ${this.currency}*\n\n`;
            message += `👤 *معلومات العميل:*\n`;
            message += `• الاسم: [يرجى كتابة اسمك]\n`;
            message += `• رقم الهاتف: [يرجى كتابة رقم هاتفك]\n`;
            message += `• العنوان: [يرجى كتابة عنوانك]\n\n`;
            message += `📋 *ملاحظات إضافية:* [اختياري]\n\n`;
            message += `شكراً لاختيارك ${this.businessName}! 🌟`;
        } else if (currentLang === 'en') {
            message = `🛍️ *New Order from ${this.businessName}*\n\n`;
            message += `📋 *Order Details:*\n`;
            message += `━━━━━━━━━━━━━━━━━━━━\n`;

            cartItems.forEach((item, index) => {
                const itemTotal = item.price * item.quantity;
                total += itemTotal;
                
                message += `${index + 1}. *${item.name}*\n`;
                message += `   💰 Price: ${item.price} ${this.currency}\n`;
                message += `   📦 Quantity: ${item.quantity}\n`;
                message += `   💵 Total: ${itemTotal} ${this.currency}\n\n`;
            });

            message += `━━━━━━━━━━━━━━━━━━━━\n`;
            message += `💰 *Grand Total: ${total.toFixed(2)} ${this.currency}*\n\n`;
            message += `👤 *Customer Information:*\n`;
            message += `• Name: [Please enter your name]\n`;
            message += `• Phone: [Please enter your phone number]\n`;
            message += `• Address: [Please enter your address]\n\n`;
            message += `📋 *Additional Notes:* [Optional]\n\n`;
            message += `Thank you for choosing ${this.businessName}! 🌟`;
        } else { // Turkish
            message = `🛍️ *${this.businessName}'den Yeni Sipariş*\n\n`;
            message += `📋 *Sipariş Detayları:*\n`;
            message += `━━━━━━━━━━━━━━━━━━━━\n`;

            cartItems.forEach((item, index) => {
                const itemTotal = item.price * item.quantity;
                total += itemTotal;
                
                message += `${index + 1}. *${item.name}*\n`;
                message += `   💰 Fiyat: ${item.price} ${this.currency}\n`;
                message += `   📦 Adet: ${item.quantity}\n`;
                message += `   💵 Toplam: ${itemTotal} ${this.currency}\n\n`;
            });

            message += `━━━━━━━━━━━━━━━━━━━━\n`;
            message += `💰 *Genel Toplam: ${total.toFixed(2)} ${this.currency}*\n\n`;
            message += `👤 *Müşteri Bilgileri:*\n`;
            message += `• İsim: [Lütfen adınızı yazın]\n`;
            message += `• Telefon: [Lütfen telefon numaranızı yazın]\n`;
            message += `• Adres: [Lütfen adresinizi yazın]\n\n`;
            message += `📋 *Ek Notlar:* [İsteğe bağlı]\n\n`;
            message += `${this.businessName}'i seçtiğiniz için teşekkürler! 🌟`;
        }

        return encodeURIComponent(message);
    }

    // فتح الواتساب
    openWhatsApp(message) {
        const url = `https://wa.me/${this.phoneNumber}?text=${message}`;
        
        // فتح في نافذة جديدة
        const newWindow = window.open(url, '_blank');
        
        // التحقق من فتح النافذة بنجاح
        if (newWindow) {
            // إظهار رسالة نجاح
            this.showNotification(
                'تم فتح الواتساب',
                'سيتم توجيهك إلى الواتساب لإرسال طلبك',
                'success'
            );
        } else {
            // في حالة منع النوافذ المنبثقة
            this.showNotification(
                'يرجى السماح بالنوافذ المنبثقة',
                'لإرسال الطلب عبر الواتساب',
                'warning'
            );
            
            // محاولة التوجيه المباشر
            setTimeout(() => {
                window.location.href = url;
            }, 2000);
        }
    }

    // الحصول على عناصر السلة
    getCartItems() {
        // التحقق من وجود نظام السلة
        if (window.app && window.app.cart && window.app.cart.items) {
            return window.app.cart.items;
        }
        
        // محاولة الحصول من localStorage
        try {
            const cartData = localStorage.getItem('cart');
            return cartData ? JSON.parse(cartData) : [];
        } catch (error) {
            console.error('Error getting cart items:', error);
            return [];
        }
    }

    // تحديث زر الواتساب في السلة
    updateCartWhatsAppButton() {
        const cartFooter = document.getElementById('cart-footer');
        if (!cartFooter) return;

        // إزالة الزر القديم إن وجد
        const existingBtn = cartFooter.querySelector('.whatsapp-order-btn');
        if (existingBtn) {
            existingBtn.remove();
        }

        // إضافة زر الواتساب الجديد
        const whatsappBtn = this.createWhatsAppButton();
        cartFooter.appendChild(whatsappBtn);
    }

    // إنشاء زر الواتساب
    createWhatsAppButton() {
        const currentLang = document.documentElement.lang || 'ar';
        const button = document.createElement('button');
        button.type = 'button';
        button.className = 'whatsapp-order-btn';
        
        let buttonText = '';
        if (currentLang === 'ar') {
            buttonText = 'طلب عبر الواتساب';
        } else if (currentLang === 'en') {
            buttonText = 'Order via WhatsApp';
        } else {
            buttonText = 'WhatsApp ile Sipariş';
        }
        
        button.innerHTML = `
            <i class="fab fa-whatsapp"></i>
            <span>${buttonText}</span>
        `;
        
        return button;
    }

    // إظهار الإشعارات
    showNotification(title, message, type = 'info') {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button type="button" class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // إضافة الأنماط
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            padding: 1rem 1.5rem;
            max-width: 350px;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            border-left: 4px solid ${type === 'success' ? '#10b981' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
        `;

        // إضافة إلى الصفحة
        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // إخفاء الإشعار تلقائياً
        setTimeout(() => {
            this.hideNotification(notification);
        }, 5000);

        // زر الإغلاق
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.hideNotification(notification);
        });
    }

    // إخفاء الإشعار
    hideNotification(notification) {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    // تحديث رقم الهاتف
    setPhoneNumber(phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    // تحديث اسم العمل
    setBusinessName(businessName) {
        this.businessName = businessName;
    }

    // تحديث العملة
    setCurrency(currency) {
        this.currency = currency;
    }
}

// تهيئة نظام الواتساب
const whatsappIntegration = new WhatsAppIntegration();

// إتاحة النظام عالمياً
window.whatsappIntegration = whatsappIntegration;
