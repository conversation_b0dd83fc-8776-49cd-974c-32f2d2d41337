// صفحة المنتجات - وظائف متقدمة
class ProductsPage {
    constructor() {
        this.allProducts = [...products];
        this.filteredProducts = [...products];
        this.displayedProducts = [];
        this.currentPage = 1;
        this.productsPerPage = 12;
        this.viewMode = 'grid';
        this.filters = {
            search: ''
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateStats();
        this.applyFilters();
        this.renderProducts();
    }

    setupEventListeners() {
        // البحث
        const searchInput = document.getElementById('search-input');
        const clearSearch = document.getElementById('clear-search');

        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value;
                this.toggleClearSearch();
                this.showSearchSuggestions(e.target.value);
                this.debounceFilter();
            });

            // إخفاء الاقتراحات عند النقر خارجها
            searchInput.addEventListener('blur', () => {
                setTimeout(() => this.hideSearchSuggestions(), 200);
            });

            // التنقل بالكيبورد في الاقتراحات
            searchInput.addEventListener('keydown', (e) => {
                this.handleSearchKeyboard(e);
            });
        }

        if (clearSearch) {
            clearSearch.addEventListener('click', () => {
                searchInput.value = '';
                this.filters.search = '';
                this.toggleClearSearch();
                this.hideSearchSuggestions();
                this.applyFilters();
            });
        }

        // إضافة زر البحث الصوتي
        this.addVoiceSearchButton();
    }

    // إضافة زر البحث الصوتي
    addVoiceSearchButton() {
        const searchWrapper = document.querySelector('.search-input-wrapper');
        const searchInput = document.getElementById('search-input');

        if (!searchWrapper || !searchInput) return;

        // التحقق من دعم البحث الصوتي
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            return;
        }

        const voiceBtn = document.createElement('button');
        voiceBtn.type = 'button';
        voiceBtn.className = 'voice-search-btn';
        voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
        voiceBtn.title = 'البحث الصوتي';
        voiceBtn.setAttribute('aria-label', 'البحث الصوتي');

        voiceBtn.addEventListener('click', () => {
            this.startVoiceSearch();
        });

        searchWrapper.appendChild(voiceBtn);
    }

    // بدء البحث الصوتي
    startVoiceSearch() {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();

        recognition.lang = getCurrentLanguage() === 'ar' ? 'ar-SA' :
                         getCurrentLanguage() === 'en' ? 'en-US' : 'tr-TR';
        recognition.continuous = false;
        recognition.interimResults = false;

        const voiceBtn = document.querySelector('.voice-search-btn');
        const searchInput = document.getElementById('search-input');

        recognition.onstart = () => {
            voiceBtn.classList.add('listening');
            voiceBtn.innerHTML = '<i class="fas fa-microphone-slash"></i>';
        };

        recognition.onresult = (event) => {
            const transcript = event.results[0][0].transcript;
            searchInput.value = transcript;
            this.filters.search = transcript;
            this.toggleClearSearch();
            this.applyFilters();
        };

        recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
        };

        recognition.onend = () => {
            voiceBtn.classList.remove('listening');
            voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
        };

        recognition.start();
    }

    // عرض اقتراحات البحث
    showSearchSuggestions(query) {
        if (!query || query.length < 2) {
            this.hideSearchSuggestions();
            return;
        }

        const suggestions = this.generateSearchSuggestions(query);
        if (suggestions.length === 0) {
            this.hideSearchSuggestions();
            return;
        }

        let suggestionsContainer = document.getElementById('search-suggestions');
        if (!suggestionsContainer) {
            suggestionsContainer = document.createElement('div');
            suggestionsContainer.id = 'search-suggestions';
            suggestionsContainer.className = 'search-suggestions';

            const searchWrapper = document.querySelector('.search-input-wrapper');
            searchWrapper.appendChild(suggestionsContainer);
        }

        suggestionsContainer.innerHTML = suggestions.map((suggestion, index) => `
            <div class="suggestion-item ${index === 0 ? 'active' : ''}" data-suggestion="${suggestion}">
                <i class="fas fa-search"></i>
                <span>${this.highlightSuggestion(suggestion, query)}</span>
            </div>
        `).join('');

        suggestionsContainer.style.display = 'block';

        // إضافة مستمعي الأحداث للاقتراحات
        suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                const suggestion = item.dataset.suggestion;
                const searchInput = document.getElementById('search-input');
                searchInput.value = suggestion;
                this.filters.search = suggestion;
                this.hideSearchSuggestions();
                this.applyFilters();
            });
        });
    }

    // إخفاء اقتراحات البحث
    hideSearchSuggestions() {
        const suggestionsContainer = document.getElementById('search-suggestions');
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
    }

    // توليد اقتراحات البحث
    generateSearchSuggestions(query) {
        const currentLang = getCurrentLanguage();
        const queryLower = query.toLowerCase();
        const suggestions = new Set();

        // البحث في أسماء المنتجات
        this.allProducts.forEach(product => {
            const name = product.name[currentLang].toLowerCase();
            const words = name.split(' ');

            // إضافة الكلمات التي تبدأ بالاستعلام
            words.forEach(word => {
                if (word.startsWith(queryLower) && word.length > queryLower.length) {
                    suggestions.add(word);
                }
            });

            // إضافة الأسماء الكاملة التي تحتوي على الاستعلام
            if (name.includes(queryLower)) {
                suggestions.add(product.name[currentLang]);
            }
        });

        // تحويل إلى مصفوفة وترتيب
        return Array.from(suggestions)
            .slice(0, 5) // أقصى 5 اقتراحات
            .sort((a, b) => {
                // ترتيب حسب الطول والصلة
                if (a.startsWith(queryLower) && !b.startsWith(queryLower)) return -1;
                if (!a.startsWith(queryLower) && b.startsWith(queryLower)) return 1;
                return a.length - b.length;
            });
    }

    // تمييز الاقتراح
    highlightSuggestion(suggestion, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return suggestion.replace(regex, '<strong>$1</strong>');
    }

    // التعامل مع الكيبورد في البحث
    handleSearchKeyboard(e) {
        const suggestionsContainer = document.getElementById('search-suggestions');
        if (!suggestionsContainer || suggestionsContainer.style.display === 'none') return;

        const suggestions = suggestionsContainer.querySelectorAll('.suggestion-item');
        const activeSuggestion = suggestionsContainer.querySelector('.suggestion-item.active');
        let activeIndex = Array.from(suggestions).indexOf(activeSuggestion);

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                activeIndex = (activeIndex + 1) % suggestions.length;
                this.setActiveSuggestion(suggestions, activeIndex);
                break;

            case 'ArrowUp':
                e.preventDefault();
                activeIndex = activeIndex <= 0 ? suggestions.length - 1 : activeIndex - 1;
                this.setActiveSuggestion(suggestions, activeIndex);
                break;

            case 'Enter':
                if (activeSuggestion) {
                    e.preventDefault();
                    activeSuggestion.click();
                }
                break;

            case 'Escape':
                this.hideSearchSuggestions();
                break;
        }
    }

    // تعيين الاقتراح النشط
    setActiveSuggestion(suggestions, activeIndex) {
        suggestions.forEach((suggestion, index) => {
            suggestion.classList.toggle('active', index === activeIndex);
        });
        }

        // إعادة تعيين الفلاتر
        const resetFilters = document.getElementById('reset-filters');

        if (resetFilters) {
            resetFilters.addEventListener('click', () => {
                this.resetAllFilters();
            });
        }

        // تحميل المزيد
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreProducts();
            });
        }

        // أزرار إضافة للسلة
        document.addEventListener('click', (e) => {
            if (e.target.closest('.add-to-cart')) {
                const productId = parseInt(e.target.closest('.add-to-cart').dataset.productId);
                if (typeof addToCart === 'function') {
                    addToCart(productId);
                }
            }
        });
    }



    updateStats() {
        const totalProducts = document.getElementById('total-products');

        if (totalProducts) {
            totalProducts.textContent = this.allProducts.length;
        }
    }

    toggleClearSearch() {
        const clearSearch = document.getElementById('clear-search');
        if (clearSearch) {
            if (this.filters.search) {
                clearSearch.classList.remove('hidden');
            } else {
                clearSearch.classList.add('hidden');
            }
        }
    }

    debounceFilter() {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.applyFilters();
        }, 300);
    }

    applyFilters() {
        this.showLoading();

        setTimeout(() => {
            let filtered = [...this.allProducts];
            const currentLang = getCurrentLanguage();

            // تطبيق فلتر البحث المحسن
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase().trim();

                if (searchTerm) {
                    filtered = filtered.filter(product => {
                        const name = product.name[currentLang].toLowerCase();
                        const description = product.description[currentLang].toLowerCase();
                        const price = product.price.toString();

                        // البحث في الاسم والوصف والسعر
                        return name.includes(searchTerm) ||
                               description.includes(searchTerm) ||
                               price.includes(searchTerm) ||
                               this.fuzzySearch(name, searchTerm) ||
                               this.fuzzySearch(description, searchTerm);
                    });

                    // ترتيب النتائج حسب الصلة
                    filtered = this.sortByRelevance(filtered, searchTerm, currentLang);
                }
            }

            // إذا لم يكن هناك بحث، ترتيب حسب الاسم
            if (!this.filters.search) {
                filtered.sort((a, b) => {
                    return a.name[currentLang].localeCompare(b.name[currentLang]);
                });
            }

            this.filteredProducts = filtered;
            this.currentPage = 1;
            this.updateActiveFilters();
            this.updateResultsInfo();
            this.renderProducts();
            this.hideLoading();

            // إضافة تمييز النص المطابق
            this.highlightSearchResults();
        }, 300);
    }

    // بحث ضبابي للكلمات المشابهة
    fuzzySearch(text, searchTerm) {
        if (searchTerm.length < 3) return false;

        const words = text.split(' ');
        return words.some(word => {
            if (word.length < 3) return false;

            const distance = this.levenshteinDistance(word, searchTerm);
            const threshold = Math.floor(searchTerm.length * 0.3); // 30% خطأ مسموح

            return distance <= threshold;
        });
    }

    // حساب المسافة بين الكلمات
    levenshteinDistance(str1, str2) {
        const matrix = [];

        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }

        return matrix[str2.length][str1.length];
    }

    // ترتيب النتائج حسب الصلة
    sortByRelevance(products, searchTerm, currentLang) {
        return products.sort((a, b) => {
            const aName = a.name[currentLang].toLowerCase();
            const bName = b.name[currentLang].toLowerCase();
            const aDesc = a.description[currentLang].toLowerCase();
            const bDesc = b.description[currentLang].toLowerCase();

            // نقاط الصلة
            let aScore = 0;
            let bScore = 0;

            // مطابقة كاملة في الاسم (أعلى نقاط)
            if (aName.includes(searchTerm)) aScore += 100;
            if (bName.includes(searchTerm)) bScore += 100;

            // مطابقة في بداية الاسم
            if (aName.startsWith(searchTerm)) aScore += 50;
            if (bName.startsWith(searchTerm)) bScore += 50;

            // مطابقة في الوصف
            if (aDesc.includes(searchTerm)) aScore += 25;
            if (bDesc.includes(searchTerm)) bScore += 25;

            // المنتجات المميزة تحصل على نقاط إضافية
            if (a.featured) aScore += 10;
            if (b.featured) bScore += 10;

            return bScore - aScore;
        });
    }

    // تمييز النص المطابق
    highlightSearchResults() {
        if (!this.filters.search) return;

        const searchTerm = this.filters.search.toLowerCase().trim();
        if (!searchTerm) return;

        setTimeout(() => {
            const productCards = document.querySelectorAll('.product-card');
            productCards.forEach(card => {
                const title = card.querySelector('.product-title');
                const description = card.querySelector('.product-description');

                if (title) {
                    title.innerHTML = this.highlightText(title.textContent, searchTerm);
                }

                if (description) {
                    description.innerHTML = this.highlightText(description.textContent, searchTerm);
                }
            });
        }, 100);
    }

    // تمييز النص
    highlightText(text, searchTerm) {
        if (!searchTerm || searchTerm.length < 2) return text;

        const regex = new RegExp(`(${searchTerm})`, 'gi');
        return text.replace(regex, '<mark class="search-highlight">$1</mark>');
    }

    updateActiveFilters() {
        // لا حاجة لعرض الفلاتر النشطة بعد الآن
    }



    resetAllFilters() {
        this.filters = {
            search: ''
        };

        // إعادة تعيين عناصر الواجهة
        const searchInput = document.getElementById('search-input');

        if (searchInput) searchInput.value = '';

        this.toggleClearSearch();
        this.applyFilters();
    }

    updateResultsInfo() {
        const showingCount = document.getElementById('showing-count');
        const totalCount = document.getElementById('total-count');

        if (showingCount && totalCount) {
            const showing = Math.min(this.currentPage * this.productsPerPage, this.filteredProducts.length);
            showingCount.textContent = showing;
            totalCount.textContent = this.filteredProducts.length;
        }
    }



    renderProducts() {
        const productsGrid = document.getElementById('products-grid');
        const noResults = document.getElementById('no-results');
        const loadMoreContainer = document.getElementById('load-more-container');

        if (!productsGrid) return;

        if (this.filteredProducts.length === 0) {
            productsGrid.innerHTML = '';
            if (noResults) noResults.classList.remove('hidden');
            if (loadMoreContainer) loadMoreContainer.classList.add('hidden');
            return;
        }

        if (noResults) noResults.classList.add('hidden');

        // حساب المنتجات المعروضة
        const endIndex = this.currentPage * this.productsPerPage;
        this.displayedProducts = this.filteredProducts.slice(0, endIndex);

        const currentLang = getCurrentLanguage();
        productsGrid.innerHTML = this.displayedProducts.map((product, index) => `
            <div class="product-card fade-in" style="animation-delay: ${index * 0.1}s">
                <div class="product-image">
                    <img src="${product.image}" alt="${product.name[currentLang]}" loading="lazy">
                    ${product.featured ? `<div class="product-badge">${t('featured')}</div>` : ''}
                </div>
                <div class="product-info">
                    <h3 class="product-title">${product.name[currentLang]}</h3>
                    <p class="product-description">${product.description[currentLang]}</p>
                    <div class="product-footer">
                        <span class="product-price">$${product.price}</span>
                        <button type="button" class="add-to-cart" data-product-id="${product.id}">
                            <i class="fas fa-shopping-cart"></i>
                            ${t('addToCart')}
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        // إظهار/إخفاء زر تحميل المزيد
        if (loadMoreContainer) {
            if (this.displayedProducts.length < this.filteredProducts.length) {
                loadMoreContainer.classList.remove('hidden');
            } else {
                loadMoreContainer.classList.add('hidden');
            }
        }

        this.updateResultsInfo();
    }

    loadMoreProducts() {
        this.currentPage++;
        this.renderProducts();
    }

    showLoading() {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.classList.remove('hidden');
        }
    }

    hideLoading() {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.classList.add('hidden');
        }
    }
}

// تهيئة صفحة المنتجات عند تحميل الصفحة
let productsPage;

document.addEventListener('DOMContentLoaded', function() {
    // التحقق من أننا في صفحة المنتجات
    if (document.getElementById('products-grid')) {
        productsPage = new ProductsPage();
    }
});
