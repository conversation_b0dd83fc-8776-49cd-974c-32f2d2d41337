/* تحسينات إضافية للتصميم */

/* تحسين الانتقالات والرسوم المتحركة */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تحسين الأداء للرسوم المتحركة */
.service-card,
.product-card,
.work-card,
.btn,
.add-to-cart,
.cart-btn,
.theme-toggle,
.lang-btn {
    will-change: transform;
}

/* تأثيرات بصرية محسنة */
.hero {
    background-attachment: fixed;
}

.hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(to top, var(--bg-primary), transparent);
    pointer-events: none;
}

/* تحسين الظلال للوضع الليلي */
.dark-mode .service-card,
.dark-mode .product-card,
.dark-mode .work-card {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

.dark-mode .service-card:hover,
.dark-mode .product-card:hover,
.dark-mode .work-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* تحسين الهيدر للوضع الليلي */
.dark-mode .header {
    border-bottom-color: var(--border-color);
}

/* تحسين النصوص للوضع الليلي */
.dark-mode .hero-title {
    background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسين الأزرار للوضع الليلي */
.dark-mode .btn-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
}

.dark-mode .btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-400), var(--primary-500));
    box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.4);
}

.dark-mode .btn-outline {
    border-color: rgba(255, 255, 255, 0.6);
    color: rgba(255, 255, 255, 0.9);
}

.dark-mode .btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.8);
    color: white;
}

/* تحسين النماذج للوضع الليلي */
.dark-mode .form-group input,
.dark-mode .form-group textarea {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

.dark-mode .form-group input:focus,
.dark-mode .form-group textarea:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* تحسين الإشعارات للوضع الليلي */
.dark-mode .notification {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* تحسين السلة للوضع الليلي */
.dark-mode .cart-item {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

.dark-mode .quantity-btn {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

.dark-mode .quantity-btn:hover {
    background: var(--bg-accent);
}

/* تأثيرات التمرير المحسنة */
.scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* تحسين التنقل للوضع الليلي */
.dark-mode .nav-menu {
    background: rgba(15, 23, 42, 0.95);
    border-color: var(--border-color);
}

.dark-mode .lang-menu {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* تحسين الصور */
img {
    transition: var(--transition);
}

.product-image img:hover,
.work-image img:hover,
.hero-image img:hover {
    filter: brightness(1.05) contrast(1.05);
}

.dark-mode .product-image img:hover,
.dark-mode .work-image img:hover,
.dark-mode .hero-image img:hover {
    filter: brightness(1.1) contrast(1.1);
}

/* تحسين الروابط */
a {
    transition: var(--transition-fast);
}

/* تحسين التركيز للوصولية */
*:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.dark-mode *:focus {
    outline-color: var(--primary-400);
}

/* تحسين التمرير */
html {
    scroll-padding-top: 80px;
}

/* تحسين الخطوط للوضع الليلي */
.dark-mode {
    font-weight: 400;
}

.dark-mode .hero-title,
.dark-mode .section-title {
    font-weight: 700;
}

/* تحسين الحدود للوضع الليلي */
.dark-mode .service-card,
.dark-mode .product-card,
.dark-mode .work-card,
.dark-mode .contact-form {
    border: 1px solid var(--border-color);
}

/* تحسين الخلفيات المتدرجة */
.gradient-bg {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 50%, var(--primary-700) 100%);
}

.dark-mode .gradient-bg {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 50%, var(--primary-800) 100%);
}

/* تحسين الشبكات */
.services-grid,
.products-grid,
.works-grid {
    gap: 2rem;
}

@media (min-width: 768px) {
    .services-grid,
    .products-grid,
    .works-grid {
        gap: 2.5rem;
    }
}

/* تحسين الرسوم المتحركة للتحميل */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين الأيقونات */
.icon {
    transition: var(--transition-fast);
}

.icon:hover {
    transform: scale(1.1);
}

/* تحسين الشارات */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    background: var(--primary-100);
    color: var(--primary-800);
}

.dark-mode .badge {
    background: var(--primary-900);
    color: var(--primary-200);
}

/* تحسين الفواصل */
.divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-color), transparent);
    margin: 2rem 0;
}

/* تحسين الحاويات */
.container {
    transition: var(--transition);
}

/* تحسين الأقسام */
section {
    position: relative;
}

section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-color-light), transparent);
    opacity: 0.5;
}

/* إخفاء الفاصل للقسم الأول */
.hero::before {
    display: none;
}

/* تحسين الأداء */
.gpu-accelerated {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

/* تحسين الطباعة */
@media print {
    .dark-mode {
        color-adjust: exact;
        -webkit-print-color-adjust: exact;
    }
}
