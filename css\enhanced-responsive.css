/* ===================================
   Enhanced Responsive Design System
   تحسينات احترافية للتصميم المتجاوب
   =================================== */

/* ===== نظام النقاط الفاصلة المحسن ===== */
:root {
    /* نقاط الكسر الجديدة */
    --breakpoint-xs: 320px;
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
    --breakpoint-3xl: 1920px;
    
    /* مسافات متجاوبة */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* أحجام الخطوط المتجاوبة */
    --text-xs: clamp(0.7rem, 0.8vw, 0.75rem);
    --text-sm: clamp(0.8rem, 1vw, 0.875rem);
    --text-base: clamp(0.9rem, 1.2vw, 1rem);
    --text-lg: clamp(1rem, 1.4vw, 1.125rem);
    --text-xl: clamp(1.1rem, 1.6vw, 1.25rem);
    --text-2xl: clamp(1.3rem, 2vw, 1.5rem);
    --text-3xl: clamp(1.6rem, 2.5vw, 1.875rem);
    --text-4xl: clamp(2rem, 3vw, 2.25rem);
    --text-5xl: clamp(2.5rem, 4vw, 3rem);
    --text-6xl: clamp(3rem, 5vw, 3.75rem);
}

/* ===== تحسينات الحاويات ===== */
.container-fluid {
    width: 100%;
    padding: 0 var(--spacing-md);
    margin: 0 auto;
}

.container-responsive {
    width: 100%;
    max-width: 100%;
    padding: 0 var(--spacing-md);
    margin: 0 auto;
}

/* تحسينات تدريجية للحاويات */
@media (min-width: 640px) {
    .container-responsive {
        max-width: 640px;
        padding: 0 var(--spacing-lg);
    }
}

@media (min-width: 768px) {
    .container-responsive {
        max-width: 768px;
        padding: 0 var(--spacing-xl);
    }
}

@media (min-width: 1024px) {
    .container-responsive {
        max-width: 1024px;
        padding: 0 var(--spacing-2xl);
    }
}

@media (min-width: 1280px) {
    .container-responsive {
        max-width: 1280px;
        padding: 0 var(--spacing-2xl);
    }
}

@media (min-width: 1536px) {
    .container-responsive {
        max-width: 1536px;
        padding: 0 var(--spacing-3xl);
    }
}

/* ===== نظام الشبكات المتقدم ===== */
.grid-responsive {
    display: grid;
    gap: var(--spacing-lg);
    grid-template-columns: 1fr;
}

/* شبكات متجاوبة تلقائية */
.grid-auto-responsive {
    display: grid;
    gap: var(--spacing-lg);
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 280px), 1fr));
}

.grid-auto-sm-responsive {
    display: grid;
    gap: var(--spacing-md);
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 220px), 1fr));
}

.grid-auto-lg-responsive {
    display: grid;
    gap: var(--spacing-xl);
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 320px), 1fr));
}

/* شبكات متدرجة */
@media (min-width: 640px) {
    .grid-responsive {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
    }
    
    .grid-2-responsive {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .grid-responsive {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .grid-3-responsive {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1024px) {
    .grid-responsive {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-2xl);
    }
    
    .grid-4-responsive {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .grid-5-responsive {
        grid-template-columns: repeat(5, 1fr);
    }
}

@media (min-width: 1280px) {
    .grid-6-responsive {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* ===== تحسينات النصوص المتجاوبة ===== */
.text-responsive {
    font-size: var(--text-base);
    line-height: 1.6;
}

.heading-responsive {
    font-size: var(--text-2xl);
    line-height: 1.3;
    font-weight: 700;
}

.title-responsive {
    font-size: var(--text-4xl);
    line-height: 1.2;
    font-weight: 800;
}

.hero-title-responsive {
    font-size: var(--text-6xl);
    line-height: 1.1;
    font-weight: 900;
}

/* ===== تحسينات المسافات المتجاوبة ===== */
.section-responsive {
    padding: var(--spacing-2xl) 0;
}

@media (min-width: 768px) {
    .section-responsive {
        padding: var(--spacing-3xl) 0;
    }
}

@media (min-width: 1024px) {
    .section-responsive {
        padding: 4rem 0;
    }
}

@media (min-width: 1280px) {
    .section-responsive {
        padding: 5rem 0;
    }
}

/* ===== تحسينات الأزرار المتجاوبة ===== */
.btn-responsive {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--text-sm);
    min-height: 44px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

@media (min-width: 768px) {
    .btn-responsive {
        padding: var(--spacing-lg) var(--spacing-2xl);
        font-size: var(--text-base);
        min-height: 48px;
    }
}

@media (min-width: 1024px) {
    .btn-responsive {
        padding: var(--spacing-lg) var(--spacing-2xl);
        font-size: var(--text-base);
        min-height: 52px;
    }
}

/* ===== تحسينات البطاقات المتجاوبة ===== */
.card-responsive {
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

@media (min-width: 768px) {
    .card-responsive {
        padding: var(--spacing-xl);
    }
}

@media (min-width: 1024px) {
    .card-responsive {
        padding: var(--spacing-2xl);
    }
}

.card-responsive:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

@media (hover: none) and (pointer: coarse) {
    .card-responsive:hover {
        transform: none;
    }
    
    .card-responsive:active {
        transform: scale(0.98);
    }
}

/* ===== تحسينات الصور المتجاوبة ===== */
.img-responsive {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.img-hero-responsive {
    width: 100%;
    height: 250px;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
}

@media (min-width: 768px) {
    .img-hero-responsive {
        height: 350px;
    }
}

@media (min-width: 1024px) {
    .img-hero-responsive {
        height: 450px;
    }
}

@media (min-width: 1280px) {
    .img-hero-responsive {
        height: 550px;
    }
}

/* ===== تحسينات الفورم المتجاوبة ===== */
.form-responsive .form-group {
    margin-bottom: var(--spacing-lg);
}

.form-responsive input,
.form-responsive textarea,
.form-responsive select {
    width: 100%;
    padding: var(--spacing-md);
    font-size: var(--text-base);
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
    transition: var(--transition);
    min-height: 44px;
}

@media (min-width: 768px) {
    .form-responsive input,
    .form-responsive textarea,
    .form-responsive select {
        padding: var(--spacing-lg);
        min-height: 48px;
    }
}

/* ===== تحسينات التنقل المتجاوب ===== */
.nav-responsive {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

@media (min-width: 768px) {
    .nav-responsive {
        flex-direction: row;
        gap: var(--spacing-lg);
    }
}

@media (min-width: 1024px) {
    .nav-responsive {
        gap: var(--spacing-xl);
    }
}
