/* ===================================
   Cross-Browser Compatibility
   التوافق مع جميع المتصفحات
   =================================== */

/* ===== إعادة تعيين CSS للمتصفحات ===== */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* إصلاح مشاكل Internet Explorer */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .flex {
        display: -ms-flexbox;
        display: flex;
    }
    
    .flex-col {
        -ms-flex-direction: column;
        flex-direction: column;
    }
    
    .items-center {
        -ms-flex-align: center;
        align-items: center;
    }
    
    .justify-center {
        -ms-flex-pack: center;
        justify-content: center;
    }
    
    .justify-between {
        -ms-flex-pack: justify;
        justify-content: space-between;
    }
}

/* ===== إصلاحات Safari ===== */
@supports (-webkit-appearance: none) {
    .btn,
    input,
    textarea,
    select {
        -webkit-appearance: none;
        appearance: none;
    }
    
    .btn {
        -webkit-user-select: none;
        user-select: none;
        -webkit-touch-callout: none;
    }
    
    /* إصلاح مشكلة الحدود في Safari */
    .card,
    .btn {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
    
    /* إصلاح مشكلة الخطوط في Safari */
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* ===== إصلاحات Firefox ===== */
@-moz-document url-prefix() {
    .btn {
        -moz-user-select: none;
        user-select: none;
    }
    
    /* إصلاح مشكلة الحدود المنحنية في Firefox */
    .rounded,
    .rounded-lg,
    .rounded-xl {
        -moz-border-radius: var(--border-radius);
        border-radius: var(--border-radius);
    }
    
    /* إصلاح مشكلة الظلال في Firefox */
    .shadow,
    .shadow-md,
    .shadow-lg {
        -moz-box-shadow: var(--shadow-md);
        box-shadow: var(--shadow-md);
    }
}

/* ===== إصلاحات Chrome/Chromium ===== */
@supports (-webkit-background-clip: text) {
    .gradient-text {
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;
    }
}

/* ===== إصلاحات Edge ===== */
@supports (-ms-ime-align: auto) {
    .grid {
        display: -ms-grid;
        display: grid;
    }
    
    .grid-2 {
        -ms-grid-columns: 1fr 1fr;
        grid-template-columns: repeat(2, 1fr);
    }
    
    .grid-3 {
        -ms-grid-columns: 1fr 1fr 1fr;
        grid-template-columns: repeat(3, 1fr);
    }
    
    .grid-4 {
        -ms-grid-columns: 1fr 1fr 1fr 1fr;
        grid-template-columns: repeat(4, 1fr);
    }
}

/* ===== إصلاحات عامة للمتصفحات القديمة ===== */
.flexbox-fallback {
    display: table;
    width: 100%;
}

.flexbox-fallback > * {
    display: table-cell;
    vertical-align: middle;
}

/* إصلاح مشكلة الخطوط العربية */
[lang="ar"],
.arabic-text {
    font-family: 'Tajawal', 'Cairo', 'Amiri', 'Noto Sans Arabic', Arial, sans-serif;
    direction: rtl;
    text-align: right;
}

[lang="en"],
.english-text {
    font-family: 'Inter', 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: ltr;
    text-align: left;
}

[lang="tr"],
.turkish-text {
    font-family: 'Inter', 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: ltr;
    text-align: left;
}

/* ===== إصلاحات الانتقالات والرسوم المتحركة ===== */
.transition-all {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.transition-transform {
    -webkit-transition: -webkit-transform 0.3s ease;
    -moz-transition: -moz-transform 0.3s ease;
    -ms-transition: -ms-transform 0.3s ease;
    -o-transition: -o-transform 0.3s ease;
    transition: transform 0.3s ease;
}

.transition-opacity {
    -webkit-transition: opacity 0.3s ease;
    -moz-transition: opacity 0.3s ease;
    -ms-transition: opacity 0.3s ease;
    -o-transition: opacity 0.3s ease;
    transition: opacity 0.3s ease;
}

/* ===== إصلاحات التحويلات ===== */
.transform {
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
}

.scale-105 {
    -webkit-transform: scale(1.05);
    -moz-transform: scale(1.05);
    -ms-transform: scale(1.05);
    -o-transform: scale(1.05);
    transform: scale(1.05);
}

.scale-98 {
    -webkit-transform: scale(0.98);
    -moz-transform: scale(0.98);
    -ms-transform: scale(0.98);
    -o-transform: scale(0.98);
    transform: scale(0.98);
}

.translate-y-1 {
    -webkit-transform: translateY(-4px);
    -moz-transform: translateY(-4px);
    -ms-transform: translateY(-4px);
    -o-transform: translateY(-4px);
    transform: translateY(-4px);
}

/* ===== إصلاحات الخلفيات المتدرجة ===== */
.gradient-bg {
    background: #3b82f6; /* fallback */
    background: -webkit-linear-gradient(135deg, #3b82f6, #1d4ed8);
    background: -moz-linear-gradient(135deg, #3b82f6, #1d4ed8);
    background: -ms-linear-gradient(135deg, #3b82f6, #1d4ed8);
    background: -o-linear-gradient(135deg, #3b82f6, #1d4ed8);
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.gradient-text-fallback {
    color: var(--primary-600);
}

/* ===== إصلاحات الشبكات للمتصفحات القديمة ===== */
@supports not (display: grid) {
    .grid {
        display: flex;
        flex-wrap: wrap;
        margin: -0.75rem;
    }
    
    .grid > * {
        margin: 0.75rem;
        flex: 1;
        min-width: 250px;
    }
    
    .grid-2 > * {
        flex-basis: calc(50% - 1.5rem);
    }
    
    .grid-3 > * {
        flex-basis: calc(33.333% - 1.5rem);
    }
    
    .grid-4 > * {
        flex-basis: calc(25% - 1.5rem);
    }
}

/* ===== إصلاحات Flexbox للمتصفحات القديمة ===== */
@supports not (display: flex) {
    .flex {
        display: table;
        width: 100%;
    }
    
    .flex > * {
        display: table-cell;
        vertical-align: middle;
    }
    
    .items-center {
        vertical-align: middle;
    }
    
    .justify-center {
        text-align: center;
    }
}

/* ===== إصلاحات الظلال للمتصفحات القديمة ===== */
.shadow-fallback {
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* ===== إصلاحات الحدود المنحنية ===== */
.rounded-fallback {
    /* fallback for very old browsers */
    border: 1px solid var(--border-color);
}

/* ===== إصلاحات الشفافية ===== */
.opacity-fallback {
    filter: alpha(opacity=50); /* IE8 */
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)"; /* IE8 */
    opacity: 0.5;
}

/* ===== إصلاحات الخطوط المخصصة ===== */
@font-face {
    font-family: 'Tajawal-Fallback';
    src: local('Arial'), local('Helvetica');
    font-display: swap;
}

/* ===== إصلاحات الوسائط المتعددة ===== */
img {
    max-width: 100%;
    height: auto;
    -ms-interpolation-mode: bicubic; /* IE */
}

/* ===== إصلاحات النماذج ===== */
input,
textarea,
select {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

/* إصلاح مشكلة placeholder في IE */
input::-ms-input-placeholder,
textarea::-ms-input-placeholder {
    color: var(--text-tertiary);
}

input::placeholder,
textarea::placeholder {
    color: var(--text-tertiary);
    opacity: 1;
}

/* ===== إصلاحات التمرير ===== */
.scroll-smooth {
    scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
    .scroll-smooth {
        scroll-behavior: auto;
    }
}

/* ===== إصلاحات الطباعة ===== */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a,
    a:visited {
        text-decoration: underline;
    }
    
    .btn {
        border: 1px solid black;
        background: white;
        color: black;
    }
}

/* ===== إصلاحات الوصولية ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

@media (prefers-contrast: high) {
    .btn {
        border: 2px solid currentColor;
    }
    
    .card {
        border: 1px solid currentColor;
    }
}

/* ===== إصلاحات الشاشات عالية الكثافة ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .high-dpi-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}
