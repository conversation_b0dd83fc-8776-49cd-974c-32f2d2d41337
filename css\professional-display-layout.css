/* تنسيق احترافي لعرض المنتجات والأعمال */
/* Professional Display Layout for Products and Works */

/* ===== متغيرات التصميم الاحترافي ===== */
:root {
    /* ألوان التدرج الاحترافي */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    
    /* ظلال احترافية */
    --shadow-card: 0 4px 25px rgba(0, 0, 0, 0.08);
    --shadow-card-hover: 0 8px 40px rgba(0, 0, 0, 0.12);
    --shadow-card-active: 0 12px 50px rgba(0, 0, 0, 0.15);
    
    /* مساحات احترافية */
    --spacing-card: 1.5rem;
    --spacing-grid: 2rem;
    --border-radius-card: 16px;
    --border-radius-image: 12px;
    
    /* انتقالات سلسة */
    --transition-card: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease-out;
}

/* ===== تخطيط الشبكة الاحترافي ===== */
.professional-grid {
    display: grid;
    gap: var(--spacing-grid);
    padding: 2rem 0;
    position: relative;
}

/* شبكة المنتجات والأعمال */
.products-grid,
.works-grid {
    display: grid;
    gap: var(--spacing-grid);
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    padding: 1rem 0;
}

/* ===== بطاقات احترافية محسنة ===== */
.product-card,
.work-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-card);
    overflow: hidden;
    box-shadow: var(--shadow-card);
    transition: var(--transition-card);
    position: relative;
    border: 1px solid var(--border-color-light);
    height: 100%;
    display: flex;
    flex-direction: column;
    transform: translateY(0);
}

.product-card:hover,
.work-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-card-hover);
    border-color: var(--primary-300);
}

.product-card:active,
.work-card:active {
    transform: translateY(-4px);
    box-shadow: var(--shadow-card-active);
}

/* ===== صور احترافية ===== */
.product-image,
.work-image {
    position: relative;
    height: 240px;
    overflow: hidden;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-image) var(--border-radius-image) 0 0;
}

.product-image img,
.work-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-card);
    border-radius: inherit;
}

.product-card:hover .product-image img,
.work-card:hover .work-image img {
    transform: scale(1.05);
}

/* شارات احترافية */
.product-badge,
.work-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 2;
    backdrop-filter: blur(10px);
}

.product-badge.featured,
.work-badge.featured {
    background: var(--gradient-warning);
}

.product-badge.new,
.work-badge.new {
    background: var(--gradient-success);
}

.product-badge.sale,
.work-badge.sale {
    background: var(--gradient-secondary);
}

/* ===== محتوى البطاقات ===== */
.product-info,
.work-info {
    padding: var(--spacing-card);
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* فئات احترافية */
.product-category,
.work-category {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--primary-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

/* عناوين احترافية */
.product-name,
.work-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.4;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* أوصاف احترافية */
.product-description,
.work-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.6;
    flex: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* ===== تذييل البطاقات ===== */
.product-footer,
.work-footer {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color-light);
}

/* مجموعة الأزرار */
.product-actions {
    display: flex;
    gap: 0.75rem;
    width: 100%;
}

/* أسعار احترافية */
.product-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-600);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.product-price::before {
    content: '$';
    font-size: 0.875rem;
    opacity: 0.8;
}

/* معلومات العمل */
.work-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.work-client {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.work-year {
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

/* ===== أزرار احترافية ===== */
.add-to-cart,
.view-work-btn,
.whatsapp-order-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-fast);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-width: 120px;
    justify-content: center;
}

/* زر إضافة للسلة */
.add-to-cart {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    flex: 1;
}

.add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* زر عرض العمل */
.view-work-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.view-work-btn:hover {
    background: var(--primary-600);
    color: white;
    border-color: var(--primary-600);
    transform: translateY(-2px);
}

/* زر طلب واتساب */
.whatsapp-order-btn {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    flex: 1;
}

.whatsapp-order-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
}

.whatsapp-order-btn i {
    font-size: 1rem;
}

/* ===== تحسينات الاستجابة ===== */

/* الشاشات الكبيرة جداً */
@media (min-width: 1400px) {
    .products-grid,
    .works-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 2.5rem;
    }
    
    .product-image,
    .work-image {
        height: 280px;
    }
    
    .product-info,
    .work-info {
        padding: 2rem;
    }
}

/* الشاشات الكبيرة */
@media (min-width: 1200px) and (max-width: 1399px) {
    .products-grid,
    .works-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
    
    .product-image,
    .work-image {
        height: 260px;
    }
}

/* الشاشات المتوسطة */
@media (min-width: 768px) and (max-width: 1199px) {
    .products-grid,
    .works-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .product-image,
    .work-image {
        height: 220px;
    }
    
    .product-info,
    .work-info {
        padding: 1.25rem;
    }
}

/* الهواتف المحمولة */
@media (max-width: 767px) {
    .products-grid,
    .works-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 1rem 0;
    }
    
    .product-image,
    .work-image {
        height: 200px;
    }
    
    .product-info,
    .work-info {
        padding: 1.25rem;
        gap: 1rem;
    }
    
    .product-footer,
    .work-footer {
        gap: 1rem;
    }

    .product-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .add-to-cart,
    .view-work-btn,
    .whatsapp-order-btn {
        width: 100%;
        padding: 1rem;
        font-size: 0.9rem;
        flex: none;
    }
    
    .product-name,
    .work-title {
        font-size: 1.1rem;
    }
    
    .product-price {
        font-size: 1.375rem;
        justify-content: center;
    }
}

/* الهواتف الصغيرة */
@media (max-width: 480px) {
    .professional-grid {
        padding: 1rem 0;
    }
    
    .products-grid,
    .works-grid {
        gap: 1rem;
    }
    
    .product-card,
    .work-card {
        margin: 0 0.5rem;
    }
}

/* ===== تحسينات الوضع الليلي ===== */
[data-theme="dark"] .product-card,
[data-theme="dark"] .work-card {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .product-card:hover,
[data-theme="dark"] .work-card:hover {
    border-color: var(--primary-400);
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .view-work-btn {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
}

[data-theme="dark"] .view-work-btn:hover {
    background: var(--primary-600);
    border-color: var(--primary-600);
}

/* ===== تأثيرات تحميل ===== */
.product-card.loading,
.work-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.product-card.loading::after,
.work-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}
