/* ===================================
   Mobile-First Professional Enhancements
   تحسينات احترافية للهواتف المحمولة
   =================================== */

/* ===== تحسينات الأساس للهواتف ===== */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
}

html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    touch-action: manipulation;
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* ===== تحسينات التفاعل للمس ===== */
.touch-target {
    min-width: 44px;
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    touch-action: manipulation;
}

.touch-target-lg {
    min-width: 48px;
    min-height: 48px;
}

.touch-target-xl {
    min-width: 56px;
    min-height: 56px;
}

/* تحسين الأزرار للمس */
.btn-touch {
    min-height: 44px;
    padding: 0.75rem 1.5rem;
    touch-action: manipulation;
    -webkit-user-select: none;
    user-select: none;
}

.btn-touch:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

/* ===== تحسينات النماذج للهواتف ===== */
.form-mobile input,
.form-mobile textarea,
.form-mobile select {
    min-height: 44px;
    padding: 0.875rem 1rem;
    font-size: 16px; /* منع التكبير في iOS */
    border-radius: 8px;
    border: 2px solid var(--border-color);
    transition: all 0.2s ease;
    -webkit-appearance: none;
    appearance: none;
}

.form-mobile input:focus,
.form-mobile textarea:focus,
.form-mobile select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: scale(1.02);
}

.form-mobile label {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
    color: var(--text-primary);
}

/* ===== تحسينات التنقل للهواتف ===== */
.mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 1000;
    border-bottom: 1px solid var(--border-color-light);
}

.mobile-nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    min-height: 64px;
}

.mobile-menu {
    position: fixed;
    top: 64px;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-primary);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 999;
    overflow-y: auto;
    padding: 2rem 1rem;
}

.mobile-menu.active {
    transform: translateX(0);
}

.mobile-menu-item {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color-light);
}

.mobile-menu-link {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.mobile-menu-link:active {
    background: var(--bg-secondary);
    transform: scale(0.98);
}

/* ===== تحسينات البطاقات للهواتف ===== */
.card-mobile {
    padding: 1.25rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background: var(--bg-primary);
    border: 1px solid var(--border-color-light);
    transition: all 0.2s ease;
    margin-bottom: 1rem;
}

.card-mobile:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.card-mobile-header {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color-light);
}

.card-mobile-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.card-mobile-subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.card-mobile-body {
    margin-bottom: 1rem;
}

.card-mobile-footer {
    padding-top: 0.75rem;
    border-top: 1px solid var(--border-color-light);
}

/* ===== تحسينات الشبكات للهواتف ===== */
.grid-mobile {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
}

.grid-mobile-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

@media (min-width: 375px) {
    .grid-mobile-2 {
        gap: 1rem;
    }
}

@media (min-width: 414px) {
    .grid-mobile {
        gap: 1.25rem;
    }
    
    .grid-mobile-2 {
        gap: 1.25rem;
    }
}

/* ===== تحسينات النصوص للهواتف ===== */
.text-mobile-xs { font-size: 0.75rem; line-height: 1.4; }
.text-mobile-sm { font-size: 0.875rem; line-height: 1.5; }
.text-mobile-base { font-size: 1rem; line-height: 1.6; }
.text-mobile-lg { font-size: 1.125rem; line-height: 1.5; }
.text-mobile-xl { font-size: 1.25rem; line-height: 1.4; }
.text-mobile-2xl { font-size: 1.5rem; line-height: 1.3; }
.text-mobile-3xl { font-size: 1.875rem; line-height: 1.2; }

.heading-mobile {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.title-mobile {
    font-size: 2rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

/* ===== تحسينات المسافات للهواتف ===== */
.section-mobile {
    padding: 2rem 0;
}

.container-mobile {
    padding: 0 1rem;
    margin: 0 auto;
    max-width: 100%;
}

@media (min-width: 375px) {
    .container-mobile {
        padding: 0 1.25rem;
    }
}

@media (min-width: 414px) {
    .container-mobile {
        padding: 0 1.5rem;
    }
    
    .section-mobile {
        padding: 2.5rem 0;
    }
}

/* ===== تحسينات الصور للهواتف ===== */
.img-mobile {
    width: 100%;
    height: auto;
    border-radius: 8px;
    object-fit: cover;
}

.img-mobile-hero {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 12px;
}

@media (min-width: 375px) {
    .img-mobile-hero {
        height: 240px;
    }
}

@media (min-width: 414px) {
    .img-mobile-hero {
        height: 280px;
    }
}

/* ===== تحسينات الأيقونات للهواتف ===== */
.icon-mobile {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: var(--primary-100);
    color: var(--primary-600);
    font-size: 1.25rem;
}
 .logo-text{
        display: none;
    }

.icon-mobile-sm {
    width: 32px;
    height: 32px;
    font-size: 1rem;
}

.icon-mobile-lg {
    width: 48px;
    height: 48px;
    font-size: 1.5rem;
}

/* ===== تحسينات الحالة للهواتف ===== */
.loading-mobile {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: var(--text-secondary);
}

.spinner-mobile {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.75rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-mobile {
    text-align: center;
    padding: 2rem 1rem;
    color: var(--text-secondary);
}

.error-mobile-icon {
    font-size: 3rem;
    color: var(--text-tertiary);
    margin-bottom: 1rem;
}

.error-mobile-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.error-mobile-text {
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

/* ===== تحسينات الوصولية للهواتف ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* ===== تحسينات الأداء للهواتف ===== */
.will-change-transform {
    will-change: transform;
}

.will-change-scroll {
    will-change: scroll-position;
}

.gpu-accelerated {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

/* تحسين الرسوم المتحركة للهواتف */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ===== تحسينات خاصة بـ iOS ===== */
@supports (-webkit-touch-callout: none) {
    .ios-safe-area {
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
    }
    
    .ios-scroll-fix {
        -webkit-overflow-scrolling: touch;
    }
}
