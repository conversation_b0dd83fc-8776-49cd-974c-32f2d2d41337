/* ===================================
   Professional Layout System
   نظام التخطيط الاحترافي
   =================================== */

/* ===== الهيدر الاحترافي ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border-bottom: 1px solid var(--border-color-light) !important;
    z-index: 1000;
    transition: var(--transition);
    height: 80px !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08) !important;
}

[data-theme="dark"] .header {
    background: rgba(15, 23, 42, 0.98) !important;
    border-bottom-color: var(--border-color) !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3) !important;
}

.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100% !important;
    padding: 0 !important;
    position: relative;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-600);
    text-decoration: none;
    transition: var(--transition);
    z-index: 10;
}

.logo:hover {
    transform: translateY(-2px);
}

.logo-icon {
    width: 48px !important;
    height: 48px !important;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700)) !important;
    border-radius: var(--border-radius-md) !important;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.4rem !important;
    box-shadow: var(--shadow-md) !important;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    transition: var(--transition);
    opacity: 0;
}

.logo:hover .logo-icon::before {
    opacity: 1;
    animation: shine 0.6s ease-in-out;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.logo:hover .logo-icon {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg) !important;
}

.logo-text {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--text-primary);
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, var(--text-primary), var(--primary-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== التنقل المحسن ===== */
.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: var(--transition);
    position: relative;
    padding: 0.5rem 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-600);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    transition: var(--transition);
    border-radius: 1px;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* ===== أدوات التحكم المحسنة ===== */
.nav-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-btn,
.theme-toggle,
.lang-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    min-width: 44px;
    min-height: 44px;
    justify-content: center;
}

.cart-btn:hover,
.theme-toggle:hover,
.lang-btn:hover {
    background: var(--bg-secondary);
    color: var(--primary-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.cart-btn:active,
.theme-toggle:active,
.lang-btn:active {
    transform: translateY(0);
}

.cart-count {
    position: absolute;
    top: -2px;
    right: -2px;
    background: var(--primary-500);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* ===== السلة المحسنة ===== */
.cart-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: var(--bg-primary);
    border-left: 1px solid var(--border-color);
    box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
    z-index: 1001;
    transition: var(--transition);
    overflow-y: auto;
}

.cart-sidebar.active {
    right: 0;
}

[data-theme="dark"] .cart-sidebar {
    background: var(--bg-secondary);
    border-left-color: var(--border-color);
    box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
}

.cart-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-secondary);
}

.cart-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
}

.cart-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.cart-content {
    padding: 1.5rem;
    min-height: calc(100vh - 200px);
}

.cart-empty {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.cart-empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.cart-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--border-color-light);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    background: var(--bg-primary);
    transition: var(--transition);
}

.cart-item:hover {
    box-shadow: var(--shadow-sm);
    border-color: var(--border-color);
}

.cart-item-image {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius);
    overflow: hidden;
    flex-shrink: 0;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.cart-item-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.cart-item-price {
    color: var(--primary-600);
    font-weight: 700;
    font-size: 1.1rem;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: auto;
}

.quantity-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    font-weight: 600;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-300);
}

.quantity-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.quantity-display {
    padding: 0.25rem 0.75rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    color: var(--text-primary);
    min-width: 40px;
    text-align: center;
}

.remove-item {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    margin-left: auto;
}

.remove-item:hover {
    color: var(--error-500);
    background: var(--error-50);
}

.cart-footer {
    position: sticky;
    bottom: 0;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color-light);
    padding: 1.5rem;
}

.cart-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 700;
}

.cart-total-label {
    color: var(--text-primary);
}

.cart-total-amount {
    color: var(--primary-600);
}

.checkout-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    border: none;
    padding: 1rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
}

.checkout-btn:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.checkout-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* ===== الطبقة العلوية للسلة ===== */
.cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.cart-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* ===== إشعارات السلة ===== */
.cart-notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 1rem 1.5rem;
    z-index: 1002;
    transform: translateX(100%);
    transition: var(--transition);
    max-width: 300px;
}

.cart-notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-primary);
}

.notification-content i {
    color: var(--success-500);
    font-size: 1.25rem;
}

/* ===== تحسينات الهيدر عند التمرير ===== */
.header.scrolled {
    background: rgba(255, 255, 255, 0.95) !important;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(25px) !important;
    -webkit-backdrop-filter: blur(25px) !important;
}

[data-theme="dark"] .header.scrolled {
    background: rgba(15, 23, 42, 0.95) !important;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.4) !important;
}

/* ===== تحسينات الهواتف المحمولة ===== */
@media (max-width: 768px) {
    .cart-sidebar {
        width: 100vw;
        right: -100vw;
    }

    .cart-notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100%);
    }

    .cart-notification.show {
        transform: translateY(0);
    }

    .nav-menu {
        display: none;
        position: fixed;
        top: var(--header-height);
        left: 0;
        right: 0;
        background: var(--bg-primary);
        border-top: 1px solid var(--border-color);
        padding: 2rem 1rem;
        flex-direction: column;
        gap: 1rem;
        z-index: 999;
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-link {
        padding: 1rem;
        border-radius: var(--border-radius);
        background: var(--bg-secondary);
        text-align: center;
        font-weight: 600;
    }

    .mobile-toggle {
        display: flex;
        flex-direction: column;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.5rem;
        gap: 4px;
        width: 44px;
        height: 44px;
        align-items: center;
        justify-content: center;
    }

    .mobile-toggle span {
        width: 25px;
        height: 3px;
        background: var(--text-primary);
        transition: var(--transition);
        border-radius: 2px;
    }

    .mobile-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }

    .mobile-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .mobile-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }
}

@media (min-width: 769px) {
    .mobile-toggle {
        display: none;
    }
}

/* ===== متغيرات الألوان الإضافية ===== */
:root {
    --success-50: #f0fdf4;
    --success-500: #22c55e;
    --error-50: #fef2f2;
    --error-500: #ef4444;
}
