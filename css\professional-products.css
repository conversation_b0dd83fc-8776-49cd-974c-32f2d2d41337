/* ===================================
   Professional Products Page
   صفحة المنتجات الاحترافية
   =================================== */

/* ===== هيدر صفحة المنتجات المحسن ===== */
.products-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    padding: calc(var(--header-height) + 3rem) 0 4rem 0;
    position: relative;
    overflow: hidden;
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.products-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="1.5"/></g></g></svg>');
    opacity: 0.8;
}

.products-hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(to top, var(--bg-primary), transparent);
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 2rem;
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.8);
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: var(--transition);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.breadcrumb a:hover {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.breadcrumb-separator {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

.page-title {
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 900;
    color: white;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.02em;
}

.page-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 700px;
    margin: 0 auto 3rem auto;
    line-height: 1.6;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 3rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    padding: 2rem 1.5rem;
    background: rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius-xl);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 140px;
    transition: var(--transition);
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: white;
    display: block;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
}

/* ===== أدوات التصفية والبحث ===== */
.products-filters {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color-light);
    padding: 2rem 0;
    position: sticky;
    top: var(--header-height);
    z-index: 100;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.filters-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: center;
    justify-content: space-between;
}

.search-container {
    flex: 1;
    min-width: 300px;
    max-width: 500px;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 1rem;
    color: var(--text-tertiary);
    z-index: 1;
}

.search-input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-500);
    background: var(--bg-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.clear-search {
    position: absolute;
    right: 0.75rem;
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.clear-search:hover {
    color: var(--text-primary);
    background: var(--bg-tertiary);
}

.filters-group {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-select {
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
    min-width: 120px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-500);
    background: var(--bg-primary);
}

.view-toggle {
    display: flex;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 0.25rem;
    border: 1px solid var(--border-color);
}

.view-btn {
    background: none;
    border: none;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.view-btn.active {
    background: var(--primary-500);
    color: white;
    box-shadow: var(--shadow-sm);
}

/* ===== نتائج المنتجات ===== */
.products-section {
    padding: 3rem 0;
    min-height: 60vh;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.results-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.loading-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-secondary);
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.product-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color-light);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

.product-image {
    position: relative;
    width: 100%;
    height: 220px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--primary-500);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-full);
    font-size: 0.75rem;
    font-weight: 600;
}

.product-info {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-category {
    color: var(--text-tertiary);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.product-name {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.product-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    flex: 1;
}

.product-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
}

.product-price {
    font-size: 1.25rem;
    font-weight: 800;
    color: var(--primary-600);
}

.add-to-cart {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-to-cart:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.add-to-cart:active {
    transform: translateY(0);
}

/* ===== حالة عدم وجود نتائج ===== */
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.no-results-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.no-results-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
}

.no-results-text {
    font-size: 1rem;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== تحميل المزيد ===== */
.load-more-container {
    text-align: center;
    margin-top: 3rem;
}

.load-more-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.load-more-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-300);
    transform: translateY(-1px);
}

/* ===== تحسينات الهواتف المحمولة ===== */
@media (max-width: 768px) {
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .stat-item {
        width: 100%;
        max-width: 200px;
    }
    
    .filters-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-container {
        min-width: auto;
        max-width: none;
    }
    
    .filters-group {
        justify-content: center;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .products-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .products-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .product-card {
        margin: 0 auto;
        max-width: 350px;
    }
}
