/* تحسينات الهيدر للهواتف المحمولة */
/* Mobile Header Enhancements */

/* تحسين الهيدر الأساسي للموبايل */
@media (max-width: 768px) {
    .header {
        height: 70px;
        padding: 0;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    }

    .navbar {
        padding: 0.75rem 1rem;
        height: 100%;
        align-items: center;
    }

    /* تحسين اللوجو للموبايل */
    .logo {
        font-size: 1.25rem;
        gap: 0.5rem;
        flex-shrink: 0;
    }

    .logo-icon {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        font-size: 0.875rem;
    }

    .logo-text {
        font-weight: 700;
        color: var(--primary-600);
    }

    /* تحسين أدوات التحكم */
    .nav-controls {
        gap: 0.5rem;
        align-items: center;
    }

    /* تحسين أزرار التحكم */
    .cart-btn, 
    .theme-toggle, 
    .mobile-toggle {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        background: var(--bg-secondary);
        border: 1px solid var(--border-color-light);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .cart-btn:hover, 
    .theme-toggle:hover, 
    .mobile-toggle:hover {
        background: var(--primary-600);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    /* تحسين عداد السلة */
    .cart-count {
        position: absolute;
        top: -6px;
        right: -6px;
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        font-size: 0.625rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid white;
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
        animation: pulse 2s infinite;
    }

    .cart-count.show {
        display: flex;
    }

    /* تحسين قائمة اللغات للموبايل */
    .language-selector {
        position: relative;
    }

    .lang-btn {
        padding: 0.5rem 0.75rem;
        background: var(--bg-secondary);
        border: 1px solid var(--border-color-light);
        border-radius: 8px;
        font-size: 0.75rem;
        gap: 0.25rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        min-width: 60px;
    }

    .lang-btn:hover {
        background: var(--primary-600);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .current-lang {
        font-size: 0.875rem;
    }

    .lang-menu {
        position: absolute;
        top: calc(100% + 8px);
        right: 0;
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        min-width: 140px;
        z-index: 1001;
        overflow: hidden;
        transform: translateY(-10px);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .lang-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .lang-option {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .lang-option:hover {
        background: var(--bg-secondary);
        color: var(--primary-600);
    }

    .lang-option .flag {
        font-size: 1rem;
    }

    /* تحسين زر القائمة المحمولة */
    .mobile-toggle {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 3px;
        padding: 8px;
        background: var(--bg-secondary);
        border: 1px solid var(--border-color-light);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .mobile-toggle span {
        width: 18px;
        height: 2px;
        background: currentColor;
        border-radius: 1px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform-origin: center;
    }

    .mobile-toggle:hover {
        background: var(--primary-600);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    /* تأثير تحويل زر القائمة عند الفتح */
    .mobile-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .mobile-toggle.active span:nth-child(2) {
        opacity: 0;
        transform: scale(0);
    }

    .mobile-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(5px, -5px);
    }

    /* تحسين القائمة المنسدلة للموبايل */
    .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-top: none;
        border-radius: 0 0 16px 16px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        flex-direction: column;
        gap: 0;
        display: none;
        overflow: hidden;
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        z-index: 999;
        transform: translateY(-10px);
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .nav-menu.active {
        display: flex;
        transform: translateY(0);
        opacity: 1;
    }

    .nav-item {
        border-bottom: 1px solid var(--border-color-light);
    }

    .nav-item:last-child {
        border-bottom: none;
    }

    .nav-link {
        display: block;
        padding: 1rem 1.5rem;
        color: var(--text-primary);
        text-decoration: none;
        font-weight: 500;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        position: relative;
        border-bottom: none;
    }

    .nav-link::after {
        display: none;
    }

    .nav-link:hover {
        background: var(--bg-secondary);
        color: var(--primary-600);
        padding-left: 2rem;
    }

    .nav-link.active {
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        color: white;
        font-weight: 600;
    }

    .nav-link.active:hover {
        background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
        padding-left: 2rem;
    }

    /* تحسين المحتوى الرئيسي */
    main {
        margin-top: 70px;
    }
}

/* تحسينات إضافية للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .header {
        height: 65px;
    }

    .navbar {
        padding: 0.5rem 0.75rem;
    }

    .logo {
        font-size: 1.125rem;
    }

    .logo-icon {
        width: 32px;
        height: 32px;
        font-size: 0.75rem;
    }

    .cart-btn, 
    .theme-toggle, 
    .mobile-toggle {
        width: 32px;
        height: 32px;
    }

    .lang-btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.7rem;
        min-width: 50px;
    }

    .nav-controls {
        gap: 0.375rem;
    }

    main {
        margin-top: 65px;
    }
}

/* تحسينات الوضع الليلي للموبايل */
@media (max-width: 768px) {
    [data-theme="dark"] .header {
        background: rgba(17, 24, 39, 0.98);
        border-bottom-color: rgba(55, 65, 81, 0.8);
    }

    [data-theme="dark"] .nav-menu {
        background: rgba(17, 24, 39, 0.98);
        border-color: rgba(55, 65, 81, 0.8);
    }

    [data-theme="dark"] .nav-link:hover {
        background: rgba(55, 65, 81, 0.5);
    }

    [data-theme="dark"] .lang-menu {
        background: rgba(17, 24, 39, 0.98);
        border-color: rgba(55, 65, 81, 0.8);
    }

    [data-theme="dark"] .lang-option:hover {
        background: rgba(55, 65, 81, 0.5);
    }
}

/* تأثيرات حركية للموبايل */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.nav-menu.active {
    animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.lang-menu.active {
    animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
