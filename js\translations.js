// ملف الترجمات
const translations = {
    ar: {
        // Navigation
        home: 'الرئيسية',
        about: 'من نحن',
        works: 'أعمالنا',
        products: 'المنتجات',
        cart: 'السلة',
        contact: 'اتصل بنا',

        // Home Page
        welcome: 'مرحباً بكم في موقعنا',
        heroBadge: 'أكثر من 750 مشروع ناجح',
        heroTitle: 'نحن نبني المستقبل الرقمي',
        heroSubtitle: 'فريق متخصص في تطوير المواقع والتطبيقات والحلول التقنية المبتكرة. نحول أفكارك إلى واقع رقمي يحقق أهدافك ويتفوق على توقعاتك.',
        featureTitle1: 'تطوير احترافي',
        featureText1: 'أحدث التقنيات والممارسات',
        featureTitle2: 'تصميم متجاوب',
        featureText2: 'يعمل على جميع الأجهزة',
        featureTitle3: 'دعم مستمر',
        featureText3: 'متاحون على مدار الساعة',
        viewMore: 'عرض المزيد',
        ourServices: 'خدماتنا',
        whyChooseUs: 'لماذا تختارنا؟',

        // Stats
        happyClients: 'عميل سعيد',
        projectsDone: 'مشروع مكتمل',
        yearsExperience: 'سنوات خبرة',
        customerRating: 'تقييم العملاء',

        // Services
        servicesDescription: 'نقدم مجموعة شاملة من الخدمات المتخصصة لتلبية جميع احتياجاتكم',
        webDevelopment: 'تطوير المواقع',
        webDevelopmentDesc: 'تطوير مواقع ويب حديثة ومتجاوبة',
        mobileApps: 'تطبيقات الهاتف',
        mobileAppsDesc: 'تطوير تطبيقات الهاتف المحمول',
        graphicDesign: 'التصميم الجرافيكي',
        graphicDesignDesc: 'تصميم الهويات البصرية والمواد التسويقية',
        digitalMarketing: 'التسويق الرقمي',
        digitalMarketingDesc: 'استراتيجيات التسويق الرقمي وإدارة وسائل التواصل',

        // Products
        featuredProducts: 'منتجاتنا المميزة',
        productsDescription: 'اكتشف مجموعة مختارة من أفضل منتجاتنا',
        productsTitle: 'جميع منتجاتنا',
        productsPageDescription: 'اكتشف مجموعة واسعة من المنتجات عالية الجودة مع إمكانية البحث والتصفية المتقدمة',
        addToCart: 'أضف إلى السلة',
        orderWhatsApp: 'طلب مباشر',
        price: 'السعر',
        productDetails: 'تفاصيل المنتج',
        searchPlaceholder: 'ابحث عن المنتجات...',
        category: 'الفئة',
        allCategories: 'جميع الفئات',
        priceRange: 'نطاق السعر',
        allPrices: 'جميع الأسعار',
        sortBy: 'ترتيب حسب',
        sortByName: 'الاسم',
        sortByPriceLow: 'السعر: من الأقل للأعلى',
        sortByPriceHigh: 'السعر: من الأعلى للأقل',
        sortByFeatured: 'المميزة أولاً',
        activeFilters: 'الفلاتر النشطة',
        clearAll: 'مسح الكل',
        showing: 'عرض',
        of: 'من',
        noProductsFound: 'لم يتم العثور على منتجات',
        noProductsText: 'جرب تغيير معايير البحث أو الفلترة',
        resetFilters: 'إعادة تعيين الفلاتر',
        loadMore: 'تحميل المزيد',
        totalProducts: 'منتج متاح',
        totalCategories: 'فئة مختلفة',




        viewAllNotifications: 'عرض جميع الإشعارات',

        // About
        aboutTitle: 'من نحن',
        aboutDescription: 'نحن فريق متخصص يسعى لتقديم أفضل الخدمات والمنتجات لعملائنا',
        ourVision: 'رؤيتنا',
        ourMission: 'رسالتنا',
        visionText: 'أن نكون الخيار الأول للعملاء في مجالنا',
        missionText: 'تقديم منتجات وخدمات عالية الجودة تلبي احتياجات عملائنا',
        professionalTeam: 'فريق محترف',
        professionalTeamDesc: 'فريق من الخبراء المتخصصين في مجالاتهم',
        highQuality: 'جودة عالية',
        highQualityDesc: 'نلتزم بأعلى معايير الجودة في جميع أعمالنا',
        fastExecution: 'سرعة في التنفيذ',
        fastExecutionDesc: 'نحرص على تسليم المشاريع في الوقت المحدد',

        // Works
        ourWorks: 'أعمالنا',
        worksDescription: 'استكشف مجموعة من أفضل مشاريعنا وإنجازاتنا',

        // Cart
        cartTitle: 'سلة الشراء',
        cartEmpty: 'السلة فارغة',
        cartEmptyDesc: 'لم تقم بإضافة أي منتجات إلى السلة بعد',
        total: 'المجموع',
        buyViaWhatsApp: 'شراء عبر واتساب',
        removeFromCart: 'حذف من السلة',

        // Contact
        contactTitle: 'اتصل بنا',
        contactDescription: 'نحن هنا لمساعدتك. تواصل معنا وسنقوم بالرد عليك في أقرب وقت ممكن',
        name: 'الاسم',
        email: 'البريد الإلكتروني',
        subject: 'الموضوع',
        message: 'الرسالة',
        send: 'إرسال',
        phone: 'الهاتف',
        address: 'العنوان',
        addressText: 'إسطنبول، تركيا',

        // Footer
        footerDescription: 'نحن فريق متخصص يسعى لتقديم أفضل الحلول التقنية والخدمات المبتكرة لعملائنا.',
        quickLinks: 'روابط سريعة',
        contactInfo: 'معلومات الاتصال',
        allRightsReserved: 'جميع الحقوق محفوظة.',
        madeWith: 'صُنع بـ',
        inTurkey: 'في تركيا',

        // Common
        loading: 'جاري التحميل...',
        error: 'حدث خطأ',
        success: 'تم بنجاح',
        close: 'إغلاق',
        save: 'حفظ',
        cancel: 'إلغاء',
        featured: 'مميز',
        view: 'عرض',
        addedToCart: 'تم إضافة المنتج إلى السلة',
        quantity: 'الكمية',
        subtotal: 'المجموع الفرعي',

        // Theme
        darkMode: 'الوضع الليلي',
        lightMode: 'الوضع النهاري',

        // Cart
        cartTitle: 'سلة الشراء',
        cartEmpty: 'السلة فارغة',
        cartEmptyDesc: 'لم تقم بإضافة أي منتجات إلى السلة بعد',
        total: 'المجموع',
        buyViaWhatsApp: 'شراء عبر واتساب',
        removeFromCart: 'إزالة من السلة',

        // Language
        language: 'اللغة',
        arabic: 'العربية',
        english: 'English',
        turkish: 'Türkçe',
    },

    en: {
        // Navigation
        home: 'Home',
        about: 'About Us',
        works: 'Our Works',
        products: 'Products',
        cart: 'Cart',
        contact: 'Contact Us',

        // Home Page
        welcome: 'Welcome to Our Website',
        heroBadge: 'Over 750 Successful Projects',
        heroTitle: 'We Build the Digital Future',
        heroSubtitle: 'A specialized team in developing websites, applications, and innovative technical solutions. We transform your ideas into digital reality that achieves your goals and exceeds your expectations.',
        featureTitle1: 'Professional Development',
        featureText1: 'Latest technologies and practices',
        featureTitle2: 'Responsive Design',
        featureText2: 'Works on all devices',
        featureTitle3: 'Continuous Support',
        featureText3: 'Available 24/7',
        viewMore: 'View More',
        ourServices: 'Our Services',
        whyChooseUs: 'Why Choose Us?',

        // Stats
        happyClients: 'Happy Clients',
        projectsDone: 'Projects Done',
        yearsExperience: 'Years Experience',
        customerRating: 'Customer Rating',

        // Services
        servicesDescription: 'We offer a comprehensive range of specialized services to meet all your needs',
        webDevelopment: 'Web Development',
        webDevelopmentDesc: 'Development of modern and responsive websites',
        mobileApps: 'Mobile Apps',
        mobileAppsDesc: 'Mobile application development',
        graphicDesign: 'Graphic Design',
        graphicDesignDesc: 'Visual identity and marketing materials design',
        digitalMarketing: 'Digital Marketing',
        digitalMarketingDesc: 'Digital marketing strategies and social media management',

        // Products
        featuredProducts: 'Featured Products',
        productsDescription: 'Discover a curated selection of our best products',
        productsTitle: 'All Our Products',
        productsPageDescription: 'Discover a wide range of high-quality products with advanced search and filtering capabilities',
        addToCart: 'Add to Cart',
        orderWhatsApp: 'Direct Order',
        price: 'Price',
        productDetails: 'Product Details',
        searchPlaceholder: 'Search products...',
        category: 'Category',
        allCategories: 'All Categories',
        priceRange: 'Price Range',
        allPrices: 'All Prices',
        sortBy: 'Sort by',
        sortByName: 'Name',
        sortByPriceLow: 'Price: Low to High',
        sortByPriceHigh: 'Price: High to Low',
        sortByFeatured: 'Featured First',
        activeFilters: 'Active Filters',
        clearAll: 'Clear All',
        showing: 'Showing',
        of: 'of',
        noProductsFound: 'No products found',
        noProductsText: 'Try changing your search or filter criteria',
        resetFilters: 'Reset Filters',
        loadMore: 'Load More',
        totalProducts: 'products available',
        totalCategories: 'different categories',


        viewAllNotifications: 'View all notifications',

        // About
        aboutTitle: 'About Us',
        aboutDescription: 'We are a specialized team striving to provide the best services and products to our clients',
        ourVision: 'Our Vision',
        ourMission: 'Our Mission',
        visionText: 'To be the first choice for customers in our field',
        missionText: 'Providing high-quality products and services that meet our customers\' needs',
        professionalTeam: 'Professional Team',
        professionalTeamDesc: 'A team of experts specialized in their fields',
        highQuality: 'High Quality',
        highQualityDesc: 'We adhere to the highest quality standards in all our work',
        fastExecution: 'Fast Execution',
        fastExecutionDesc: 'We ensure projects are delivered on time',

        // Works
        ourWorks: 'Our Works',
        worksDescription: 'Explore a collection of our best projects and achievements',

        // Cart
        cartTitle: 'Shopping Cart',
        cartEmpty: 'Cart is empty',
        cartEmptyDesc: 'You haven\'t added any products to your cart yet',
        total: 'Total',
        buyViaWhatsApp: 'Buy via WhatsApp',
        removeFromCart: 'Remove from Cart',

        // Contact
        contactTitle: 'Contact Us',
        contactDescription: 'We are here to help you. Contact us and we will get back to you as soon as possible',
        name: 'Name',
        email: 'Email',
        subject: 'Subject',
        message: 'Message',
        send: 'Send',
        phone: 'Phone',
        address: 'Address',
        addressText: 'Istanbul, Turkey',

        // Footer
        footerDescription: 'We are a specialized team striving to provide the best technical solutions and innovative services to our clients.',
        quickLinks: 'Quick Links',
        contactInfo: 'Contact Info',
        allRightsReserved: 'All rights reserved.',
        madeWith: 'Made with',
        inTurkey: 'in Turkey',

        // Common
        loading: 'Loading...',
        error: 'An error occurred',
        success: 'Success',
        close: 'Close',
        save: 'Save',
        cancel: 'Cancel',
        featured: 'Featured',
        view: 'View',
        addedToCart: 'Product added to cart',
        quantity: 'Quantity',
        subtotal: 'Subtotal',

        // Theme
        darkMode: 'Dark Mode',
        lightMode: 'Light Mode',

        // Language
        language: 'Language',
        arabic: 'العربية',
        english: 'English',
        turkish: 'Türkçe',
    },

    tr: {
        // Navigation
        home: 'Ana Sayfa',
        about: 'Hakkımızda',
        works: 'Çalışmalarımız',
        products: 'Ürünler',
        cart: 'Sepet',
        contact: 'İletişim',

        // Home Page
        welcome: 'Web Sitemize Hoş Geldiniz',
        heroBadge: '750\'den Fazla Başarılı Proje',
        heroTitle: 'Dijital Geleceği İnşa Ediyoruz',
        heroSubtitle: 'Web siteleri, uygulamalar ve yenilikçi teknik çözümler geliştirmede uzmanlaşmış bir ekip. Fikirlerinizi hedeflerinizi gerçekleştiren ve beklentilerinizi aşan dijital gerçekliğe dönüştürüyoruz.',
        featureTitle1: 'Profesyonel Geliştirme',
        featureText1: 'En son teknolojiler ve uygulamalar',
        featureTitle2: 'Duyarlı Tasarım',
        featureText2: 'Tüm cihazlarda çalışır',
        featureTitle3: 'Sürekli Destek',
        featureText3: '7/24 müsait',
        viewMore: 'Daha Fazla Görüntüle',
        ourServices: 'Hizmetlerimiz',
        whyChooseUs: 'Neden Bizi Seçmelisiniz?',

        // Stats
        happyClients: 'Mutlu Müşteri',
        projectsDone: 'Tamamlanan Proje',
        yearsExperience: 'Yıl Deneyim',
        customerRating: 'Müşteri Puanı',

        // Services
        servicesDescription: 'Tüm ihtiyaçlarınızı karşılamak için kapsamlı bir uzman hizmet yelpazesi sunuyoruz',
        webDevelopment: 'Web Geliştirme',
        webDevelopmentDesc: 'Modern ve duyarlı web siteleri geliştirme',
        mobileApps: 'Mobil Uygulamalar',
        mobileAppsDesc: 'Mobil uygulama geliştirme',
        graphicDesign: 'Grafik Tasarım',
        graphicDesignDesc: 'Görsel kimlik ve pazarlama materyalleri tasarımı',
        digitalMarketing: 'Dijital Pazarlama',
        digitalMarketingDesc: 'Dijital pazarlama stratejileri ve sosyal medya yönetimi',

        // Products
        featuredProducts: 'Öne Çıkan Ürünler',
        productsDescription: 'En iyi ürünlerimizden özenle seçilmiş bir koleksiyonu keşfedin',
        productsTitle: 'Tüm Ürünlerimiz',
        productsPageDescription: 'Gelişmiş arama ve filtreleme özellikleri ile geniş bir yüksek kaliteli ürün yelpazesini keşfedin',
        addToCart: 'Sepete Ekle',
        orderWhatsApp: 'Doğrudan Sipariş',
        price: 'Fiyat',
        productDetails: 'Ürün Detayları',
        searchPlaceholder: 'Ürün ara...',
        category: 'Kategori',
        allCategories: 'Tüm Kategoriler',
        priceRange: 'Fiyat Aralığı',
        allPrices: 'Tüm Fiyatlar',
        sortBy: 'Sırala',
        sortByName: 'İsim',
        sortByPriceLow: 'Fiyat: Düşükten Yükseğe',
        sortByPriceHigh: 'Fiyat: Yüksekten Düşüğe',
        sortByFeatured: 'Öne Çıkanlar İlk',
        activeFilters: 'Aktif Filtreler',
        clearAll: 'Tümünü Temizle',
        showing: 'Gösterilen',
        of: 'toplam',
        noProductsFound: 'Ürün bulunamadı',
        noProductsText: 'Arama veya filtre kriterlerinizi değiştirmeyi deneyin',
        resetFilters: 'Filtreleri Sıfırla',
        loadMore: 'Daha Fazla Yükle',
        totalProducts: 'ürün mevcut',
        totalCategories: 'farklı kategori',

        // Authentication
        loginTitle: 'Giriş Yap',
        loginSubtitle: 'Kontrol paneline erişmek için bilgilerinizi girin',
        username: 'Kullanıcı Adı',
        usernamePlaceholder: 'Kullanıcı adınızı girin',
        password: 'Şifre',
        passwordPlaceholder: 'Şifrenizi girin',
        rememberMe: 'Beni hatırla',
        login: 'Giriş Yap',
        demoCredentials: 'Giriş Bilgileri:',
        loginCredentials: 'Kullanıcı Adı:',
        loginPassword: 'Şifre:',
        demoNote: 'Otomatik doldurmak için yukarıdaki verilere tıklayın',
        backToHome: 'Ana Sayfaya Dön',
        authenticating: 'Doğrulanıyor...',

        // Dashboard
        dashboardTitle: 'Kontrol Paneli',
        dashboardBreadcrumb: 'Ana Sayfa / Kontrol Paneli',
        dashboard: 'Kontrol Paneli',
        notifications: 'Bildirimler',
        profile: 'Profil',
        logout: 'Çıkış Yap',
        backToSite: 'Siteye Dön',
        overview: 'Genel Bakış',
        overviewDesc: 'Site performansınızın hızlı özeti',
        totalOrders: 'Toplam Siparişler',
        totalRevenue: 'Toplam Gelir',
        totalCustomers: 'Toplam Müşteriler',
        salesChart: 'Satış Grafiği',
        chartPlaceholder: 'Grafik burada görüntülenecek',
        recentOrders: 'Son Siparişler',
        viewAll: 'Tümünü Görüntüle',
        productsManagement: 'Ürün Yönetimi',
        addProduct: 'Yeni Ürün Ekle',
        productName: 'Ürün Adı',
        productImage: 'Resim',
        category: 'Kategori',
        price: 'Fiyat',
        description: 'Açıklama',
        status: 'Durum',
        actions: 'İşlemler',
        totalProducts: 'Toplam Ürünler',
        publishedProducts: 'Yayınlanan Ürünler',
        draftProducts: 'Taslaklar',
        save: 'Kaydet',
        cancel: 'İptal',
        delete: 'Sil',
        viewAllNotifications: 'Tüm bildirimleri görüntüle',

        // About
        aboutTitle: 'Hakkımızda',
        aboutDescription: 'Müşterilerimize en iyi hizmet ve ürünleri sunmaya çalışan uzman bir ekibiz',
        ourVision: 'Vizyonumuz',
        ourMission: 'Misyonumuz',
        visionText: 'Alanımızda müşteriler için ilk tercih olmak',
        missionText: 'Müşterilerimizin ihtiyaçlarını karşılayan yüksek kaliteli ürün ve hizmetler sunmak',
        professionalTeam: 'Profesyonel Ekip',
        professionalTeamDesc: 'Kendi alanlarında uzmanlaşmış uzman ekip',
        highQuality: 'Yüksek Kalite',
        highQualityDesc: 'Tüm çalışmalarımızda en yüksek kalite standartlarına bağlıyız',
        fastExecution: 'Hızlı Uygulama',
        fastExecutionDesc: 'Projelerin zamanında teslim edilmesini sağlıyoruz',

        // Works
        ourWorks: 'Çalışmalarımız',
        worksDescription: 'En iyi proje ve başarılarımızdan bir koleksiyon keşfedin',

        // Cart
        cartTitle: 'Alışveriş Sepeti',
        cartEmpty: 'Sepet boş',
        cartEmptyDesc: 'Henüz sepetinize herhangi bir ürün eklemediniz',
        total: 'Toplam',
        buyViaWhatsApp: 'WhatsApp ile Satın Al',
        removeFromCart: 'Sepetten Çıkar',

        // Contact
        contactTitle: 'İletişim',
        contactDescription: 'Size yardımcı olmak için buradayız. Bizimle iletişime geçin, en kısa sürede size geri döneceğiz',
        name: 'Ad',
        email: 'E-posta',
        subject: 'Konu',
        message: 'Mesaj',
        send: 'Gönder',
        phone: 'Telefon',
        address: 'Adres',
        addressText: 'İstanbul, Türkiye',

        // Footer
        footerDescription: 'Müşterilerimize en iyi teknik çözümler ve yenilikçi hizmetler sunmaya çalışan uzman bir ekibiz.',
        quickLinks: 'Hızlı Bağlantılar',
        contactInfo: 'İletişim Bilgileri',
        allRightsReserved: 'Tüm hakları saklıdır.',
        madeWith: 'İle yapıldı',
        inTurkey: 'Türkiye\'de',

        // Common
        loading: 'Yükleniyor...',
        error: 'Bir hata oluştu',
        success: 'Başarılı',
        close: 'Kapat',
        save: 'Kaydet',
        cancel: 'İptal',
        featured: 'Öne Çıkan',
        view: 'Görüntüle',
        addedToCart: 'Ürün sepete eklendi',
        quantity: 'Miktar',
        subtotal: 'Ara Toplam',

        // Theme
        darkMode: 'Karanlık Mod',
        lightMode: 'Aydınlık Mod',

        // Language
        language: 'Dil',
        arabic: 'العربية',
        english: 'English',
        turkish: 'Türkçe',
    }
};

// دالة الترجمة
function t(key, lang = null) {
    const currentLang = lang || getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
}

// دالة للحصول على اللغة الحالية
function getCurrentLanguage() {
    return localStorage.getItem('language') || 'ar';
}

// دالة لتطبيق الترجمات على الصفحة
function applyTranslations(lang = null) {
    const currentLang = lang || getCurrentLanguage();

    // تحديث جميع العناصر التي تحتوي على data-key
    document.querySelectorAll('[data-key]').forEach(element => {
        const key = element.getAttribute('data-key');
        const translation = t(key, currentLang);

        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
            element.placeholder = translation;
        } else {
            element.textContent = translation;
        }
    });

    // تحديث اتجاه الصفحة
    document.documentElement.dir = currentLang === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = currentLang;

    // تحديث الخط
    if (currentLang === 'ar') {
        document.body.style.fontFamily = 'var(--font-arabic)';
    } else {
        document.body.style.fontFamily = 'var(--font-latin)';
    }
}
