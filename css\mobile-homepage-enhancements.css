/* ===================================
   تحسينات الصفحة الرئيسية للجوال
   Mobile Homepage Enhancements
   =================================== */

/* ===== تحسينات الهيرو للجوال ===== */
@media (max-width: 768px) {
    .hero {
        padding: calc(var(--header-height) + 1rem) 0 2rem 0;
        min-height: 70vh;
        text-align: center;
    }
    
    .hero-content {
        padding: 0 1rem;
    }
    
    .hero-title {
        font-size: clamp(1.75rem, 8vw, 2.5rem);
        line-height: 1.2;
        margin-bottom: 1rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 2rem;
        max-width: 90%;
        margin-left: auto;
        margin-right: auto;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
        max-width: 280px;
        margin: 0 auto;
    }
    
    .btn {
        padding: 1rem 2rem;
        font-size: 1rem;
        font-weight: 600;
        min-height: 50px;
        justify-content: center;
    }
}

/* ===== تحسينات الأقسام للجوال ===== */
@media (max-width: 768px) {
    .section {
        padding: 3rem 0;
    }
    
    .section-header {
        text-align: center;
        margin-bottom: 2rem;
        padding: 0 1rem;
    }
    
    .section-title {
        font-size: clamp(1.5rem, 6vw, 2rem);
        margin-bottom: 1rem;
    }
    
    .section-subtitle {
        font-size: 0.95rem;
        line-height: 1.6;
        max-width: 90%;
        margin: 0 auto;
    }
}

/* ===== تحسينات الخدمات للجوال ===== */
@media (max-width: 768px) {
    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
    }
    
    .service-card {
        padding: 2rem 1.5rem;
        text-align: center;
        max-width: 350px;
        margin: 0 auto;
    }
    
    .service-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin: 0 auto 1.5rem auto;
    }
    
    .service-title {
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }
    
    .service-description {
        font-size: 0.9rem;
        line-height: 1.6;
    }
}

/* ===== تحسينات الأعمال للجوال ===== */
@media (max-width: 768px) {
    .works-section {
        padding: 3rem 0;
    }
    
    .works-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 0 1rem;
    }
    
    .work-card {
        max-width: 380px;
        margin: 0 auto;
        border-radius: var(--border-radius-xl);
        overflow: hidden;
    }
    
    .work-image {
        height: 220px;
    }
    
    .work-content {
        padding: 1.5rem;
    }
    
    .work-title {
        font-size: 1.25rem;
        line-height: 1.3;
        margin-bottom: 0.75rem;
    }
    
    .work-description {
        font-size: 0.9rem;
        line-height: 1.5;
        margin-bottom: 1.25rem;
    }
    
    .work-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
        padding-top: 1rem;
    }
    
    .work-client {
        text-align: center;
        font-size: 0.85rem;
    }
    
    .work-details-btn {
        width: 100%;
        justify-content: center;
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* ===== تحسينات الإحصائيات للجوال ===== */
@media (max-width: 768px) {
    .stats-section {
        padding: 2.5rem 0;
        background: var(--bg-secondary);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        padding: 0 1rem;
    }
    
    .stat-card {
        text-align: center;
        padding: 1.5rem 1rem;
        background: var(--bg-primary);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-sm);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        color: var(--primary-600);
        display: block;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-size: 0.85rem;
        color: var(--text-secondary);
        font-weight: 500;
    }
}

/* ===== تحسينات الشهادات للجوال ===== */
@media (max-width: 768px) {
    .testimonials-section {
        padding: 3rem 0;
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
    }
    
    .testimonial-card {
        padding: 2rem 1.5rem;
        text-align: center;
        max-width: 350px;
        margin: 0 auto;
        background: var(--bg-primary);
        border-radius: var(--border-radius-xl);
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color-light);
    }
    
    .testimonial-text {
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
        font-style: italic;
        color: var(--text-secondary);
    }
    
    .testimonial-author {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }
    
    .author-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid var(--primary-200);
    }
    
    .author-info {
        text-align: center;
    }
    
    .author-name {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.95rem;
    }
    
    .author-role {
        font-size: 0.8rem;
        color: var(--text-tertiary);
        margin-top: 0.25rem;
    }
}

/* ===== تحسينات الاتصال للجوال ===== */
@media (max-width: 768px) {
    .contact-section {
        padding: 3rem 0;
        background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
    }
    
    .contact-content {
        text-align: center;
        padding: 0 1rem;
    }
    
    .contact-title {
        font-size: clamp(1.5rem, 6vw, 2rem);
        margin-bottom: 1rem;
        color: var(--text-primary);
    }
    
    .contact-subtitle {
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 2rem;
        color: var(--text-secondary);
        max-width: 90%;
        margin-left: auto;
        margin-right: auto;
    }
    
    .contact-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        max-width: 280px;
        margin: 0 auto;
    }
    
    .contact-btn {
        padding: 1rem 2rem;
        border-radius: var(--border-radius-lg);
        font-weight: 600;
        font-size: 1rem;
        min-height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        transition: var(--transition);
        text-decoration: none;
    }
    
    .contact-btn.primary {
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        color: white;
        border: none;
    }
    
    .contact-btn.secondary {
        background: var(--bg-primary);
        color: var(--text-primary);
        border: 2px solid var(--border-color);
    }
    
    .contact-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
}

/* ===== تحسينات عامة للجوال ===== */
@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }
    
    .hero-title {
        font-size: clamp(1.5rem, 7vw, 2rem);
    }
    
    .section-title {
        font-size: clamp(1.25rem, 5vw, 1.75rem);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .work-card,
    .service-card,
    .testimonial-card {
        max-width: 100%;
    }
    
    .btn,
    .contact-btn {
        font-size: 0.9rem;
        padding: 0.875rem 1.5rem;
    }
  
}

/* ===== تحسينات الأداء للجوال ===== */
@media (max-width: 768px) {
    /* تحسين الصور للجوال */
    .work-image img,
    .service-icon,
    .author-avatar {
        will-change: transform;
        backface-visibility: hidden;
    }
    
    /* تحسين الانتقالات للجوال */
    .work-card,
    .service-card,
    .testimonial-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    /* تقليل تأثيرات الحركة للأجهزة الضعيفة */
    @media (prefers-reduced-motion: reduce) {
        .work-card:hover,
        .service-card:hover,
        .testimonial-card:hover {
            transform: none;
        }
        
        .work-image img {
            transition: none;
        }
    }
}
