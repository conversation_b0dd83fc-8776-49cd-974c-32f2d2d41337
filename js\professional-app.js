/* ===================================
   Professional Application JavaScript
   التطبيق الاحترافي للموقع
   =================================== */

class ProfessionalApp {
    constructor() {
        this.cart = new ShoppingCart();
        this.theme = new ThemeManager();
        this.language = new LanguageManager();
        this.products = new ProductManager();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.loadInitialData();
    }

    setupEventListeners() {
        // Header interactions
        this.setupHeaderEvents();
        // Cart interactions
        this.setupCartEvents();
        // Theme and language
        this.setupControlEvents();
        // Mobile menu
        this.setupMobileMenu();
    }

    setupHeaderEvents() {
        const header = document.getElementById('header');
        if (header) {
            window.addEventListener('scroll', () => {
                if (window.scrollY > 100) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });
        }
    }

    setupCartEvents() {
        const cartBtn = document.getElementById('cart-btn');
        const cartClose = document.getElementById('cart-close');
        const cartOverlay = document.getElementById('cart-overlay');

        if (cartBtn) {
            cartBtn.addEventListener('click', () => this.cart.toggle());
        }
        if (cartClose) {
            cartClose.addEventListener('click', () => this.cart.close());
        }
        if (cartOverlay) {
            cartOverlay.addEventListener('click', () => this.cart.close());
        }
    }

    setupControlEvents() {
        const themeToggle = document.getElementById('theme-toggle');
        const langBtn = document.getElementById('lang-btn');

        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.theme.toggle());
        }
        if (langBtn) {
            langBtn.addEventListener('click', () => this.language.toggleMenu());
        }
    }

    setupMobileMenu() {
        const mobileToggle = document.getElementById('mobile-toggle');
        const navMenu = document.getElementById('nav-menu');

        if (mobileToggle && navMenu) {
            mobileToggle.addEventListener('click', () => {
                mobileToggle.classList.toggle('active');
                navMenu.classList.toggle('active');
            });
        }
    }

    initializeComponents() {
        this.theme.init();
        this.language.init();
        this.cart.init();
        this.products.init();
    }

    loadInitialData() {
        // Load products if on products page
        if (window.location.pathname.includes('products.html')) {
            this.products.loadProducts();
        }
    }
}

class ShoppingCart {
    constructor() {
        this.items = JSON.parse(localStorage.getItem('cart')) || [];
        this.isOpen = false;
    }

    init() {
        this.updateCartCount();
        this.renderCart();
    }

    toggle() {
        this.isOpen ? this.close() : this.open();
    }

    open() {
        const sidebar = document.getElementById('cart-sidebar');
        const overlay = document.getElementById('cart-overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.add('active');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden';
            this.isOpen = true;
        }
    }

    close() {
        const sidebar = document.getElementById('cart-sidebar');
        const overlay = document.getElementById('cart-overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
            this.isOpen = false;
        }
    }

    addItem(product) {
        const existingItem = this.items.find(item => item.id === product.id);
        
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.items.push({
                ...product,
                quantity: 1
            });
        }
        
        this.saveCart();
        this.updateCartCount();
        this.renderCart();
        this.showAddedNotification(product);
    }

    removeItem(productId) {
        this.items = this.items.filter(item => item.id !== productId);
        this.saveCart();
        this.updateCartCount();
        this.renderCart();
    }

    updateQuantity(productId, quantity) {
        const item = this.items.find(item => item.id === productId);
        if (item) {
            if (quantity <= 0) {
                this.removeItem(productId);
            } else {
                item.quantity = quantity;
                this.saveCart();
                this.updateCartCount();
                this.renderCart();
            }
        }
    }

    updateCartCount() {
        const countElement = document.getElementById('cart-count');
        if (countElement) {
            const totalItems = this.items.reduce((sum, item) => sum + item.quantity, 0);
            countElement.textContent = totalItems;
            countElement.style.display = totalItems > 0 ? 'flex' : 'none';
        }
    }

    renderCart() {
        const cartItems = document.getElementById('cart-items');
        const cartEmpty = document.getElementById('cart-empty');
        const cartFooter = document.getElementById('cart-footer');

        if (!cartItems || !cartEmpty || !cartFooter) return;

        if (this.items.length === 0) {
            cartEmpty.style.display = 'block';
            cartItems.style.display = 'none';
            cartFooter.style.display = 'none';
        } else {
            cartEmpty.style.display = 'none';
            cartItems.style.display = 'block';
            cartFooter.style.display = 'block';

            cartItems.innerHTML = this.items.map(item => this.createCartItemHTML(item)).join('');
            this.updateCartTotal();
        }
    }

    createCartItemHTML(item) {
        return `
            <div class="cart-item" data-id="${item.id}">
                <div class="cart-item-image">
                    <img src="${item.image}" alt="${item.name}" loading="lazy">
                </div>
                <div class="cart-item-details">
                    <h4 class="cart-item-name">${item.name}</h4>
                    <div class="cart-item-price">$${item.price}</div>
                    <div class="cart-item-controls">
                        <button type="button" class="quantity-btn" onclick="app.cart.updateQuantity(${item.id}, ${item.quantity - 1})">-</button>
                        <span class="quantity-display">${item.quantity}</span>
                        <button type="button" class="quantity-btn" onclick="app.cart.updateQuantity(${item.id}, ${item.quantity + 1})">+</button>
                        <button type="button" class="remove-item" onclick="app.cart.removeItem(${item.id})" title="حذف المنتج">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    updateCartTotal() {
        const totalElement = document.getElementById('cart-total');
        if (totalElement) {
            const total = this.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            totalElement.textContent = `$${total.toFixed(2)}`;
        }
    }

    saveCart() {
        localStorage.setItem('cart', JSON.stringify(this.items));
    }

    showAddedNotification(product) {
        // Create and show a notification
        const notification = document.createElement('div');
        notification.className = 'cart-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-check-circle"></i>
                <span>تم إضافة ${product.name} إلى السلة</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.updateThemeIcon();
    }

    toggle() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(this.currentTheme);
        this.updateThemeIcon();
        localStorage.setItem('theme', this.currentTheme);
    }

    applyTheme(theme) {
        document.body.className = theme === 'dark' ? 'dark-mode' : 'light-mode';
        document.documentElement.setAttribute('data-theme', theme);
    }

    updateThemeIcon() {
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = this.currentTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }
    }
}

class LanguageManager {
    constructor() {
        this.currentLang = localStorage.getItem('language') || 'ar';
        this.isMenuOpen = false;
    }

    init() {
        this.applyLanguage(this.currentLang);
        this.updateLanguageDisplay();
        this.setupLanguageOptions();
    }

    toggleMenu() {
        const langMenu = document.getElementById('lang-menu');
        if (langMenu) {
            this.isMenuOpen = !this.isMenuOpen;
            langMenu.style.display = this.isMenuOpen ? 'block' : 'none';
        }
    }

    setupLanguageOptions() {
        const langOptions = document.querySelectorAll('.lang-option');
        langOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                const lang = e.currentTarget.dataset.lang;
                this.setLanguage(lang);
                this.toggleMenu();
            });
        });
    }

    setLanguage(lang) {
        this.currentLang = lang;
        this.applyLanguage(lang);
        this.updateLanguageDisplay();
        localStorage.setItem('language', lang);
    }

    applyLanguage(lang) {
        document.documentElement.lang = lang;
        document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
        
        // Update text content based on translations
        if (window.translations && window.translations[lang]) {
            this.updatePageText(window.translations[lang]);
        }
    }

    updateLanguageDisplay() {
        const currentLangElement = document.querySelector('.current-lang');
        if (currentLangElement) {
            const flags = { ar: '🇸🇦', en: '🇺🇸', tr: '🇹🇷' };
            currentLangElement.textContent = flags[this.currentLang] || '🇸🇦';
        }
    }

    updatePageText(translations) {
        document.querySelectorAll('[data-key]').forEach(element => {
            const key = element.dataset.key;
            if (translations[key]) {
                if (element.tagName === 'INPUT' && element.type === 'text') {
                    element.placeholder = translations[key];
                } else {
                    element.textContent = translations[key];
                }
            }
        });
    }
}

class ProductManager {
    constructor() {
        this.products = [];
        this.filteredProducts = [];
        this.currentPage = 1;
        this.productsPerPage = 12;
    }

    init() {
        this.setupSearchAndFilters();
    }

    setupSearchAndFilters() {
        const searchInput = document.getElementById('search-input');
        const clearSearch = document.getElementById('clear-search');

        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterProducts(e.target.value);
                clearSearch.style.display = e.target.value ? 'block' : 'none';
            });
        }

        if (clearSearch) {
            clearSearch.addEventListener('click', () => {
                searchInput.value = '';
                clearSearch.style.display = 'none';
                this.filterProducts('');
            });
        }
    }

    async loadProducts() {
        try {
            // Load products from data.js or API
            if (window.productsData) {
                this.products = window.productsData;
                this.filteredProducts = [...this.products];
                this.renderProducts();
                this.updateProductCount();
            }
        } catch (error) {
            console.error('Error loading products:', error);
        }
    }

    filterProducts(searchTerm) {
        this.filteredProducts = this.products.filter(product =>
            product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            product.category.toLowerCase().includes(searchTerm.toLowerCase())
        );
        this.currentPage = 1;
        this.renderProducts();
        this.updateProductCount();
    }

    renderProducts() {
        const productsGrid = document.getElementById('products-grid');
        const noResults = document.getElementById('no-results');

        if (!productsGrid) return;

        if (this.filteredProducts.length === 0) {
            productsGrid.style.display = 'none';
            if (noResults) noResults.style.display = 'block';
        } else {
            productsGrid.style.display = 'grid';
            if (noResults) noResults.style.display = 'none';

            const startIndex = (this.currentPage - 1) * this.productsPerPage;
            const endIndex = startIndex + this.productsPerPage;
            const productsToShow = this.filteredProducts.slice(0, endIndex);

            productsGrid.innerHTML = productsToShow.map(product => this.createProductHTML(product)).join('');
        }
    }

    createProductHTML(product) {
        return `
            <div class="product-card" data-id="${product.id}">
                <div class="product-image">
                    <img src="${product.image}" alt="${product.name}" loading="lazy">
                    ${product.badge ? `<div class="product-badge">${product.badge}</div>` : ''}
                </div>
                <div class="product-info">
                    <div class="product-category">${product.category}</div>
                    <h3 class="product-name">${product.name}</h3>
                    <p class="product-description">${product.description}</p>
                    <div class="product-footer">
                        <div class="product-price">$${product.price}</div>
                        <button type="button" class="add-to-cart" onclick="app.cart.addItem(${JSON.stringify(product).replace(/"/g, '&quot;')})">
                            <i class="fas fa-cart-plus"></i>
                            <span>أضف للسلة</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    updateProductCount() {
        const totalCount = document.getElementById('total-count');
        const showingCount = document.getElementById('showing-count');
        const totalProducts = document.getElementById('total-products');

        if (totalCount) totalCount.textContent = this.filteredProducts.length;
        if (showingCount) {
            const showing = Math.min(this.currentPage * this.productsPerPage, this.filteredProducts.length);
            showingCount.textContent = showing;
        }
        if (totalProducts) totalProducts.textContent = this.products.length;
    }
}

// Initialize the application
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new ProfessionalApp();
});

// Export for global access
window.app = app;
