/* ===================================
   Professional Utility Classes
   فئات الأدوات الاحترافية
   =================================== */

/* ===== فئات العرض والإخفاء ===== */
.show { display: block !important; }
.hide { display: none !important; }
.invisible { visibility: hidden !important; }
.visible { visibility: visible !important; }

/* إخفاء/إظهار حسب حجم الشاشة */
@media (max-width: 639px) {
    .hidden-xs { display: none !important; }
    .visible-xs { display: block !important; }
}

@media (min-width: 640px) and (max-width: 767px) {
    .hidden-sm { display: none !important; }
    .visible-sm { display: block !important; }
}

@media (min-width: 768px) and (max-width: 1023px) {
    .hidden-md { display: none !important; }
    .visible-md { display: block !important; }
}

@media (min-width: 1024px) and (max-width: 1279px) {
    .hidden-lg { display: none !important; }
    .visible-lg { display: block !important; }
}

@media (min-width: 1280px) {
    .hidden-xl { display: none !important; }
    .visible-xl { display: block !important; }
}

/* ===== فئات المسافات الاحترافية ===== */
/* Margin */
.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 0.75rem !important; }
.m-4 { margin: 1rem !important; }
.m-5 { margin: 1.25rem !important; }
.m-6 { margin: 1.5rem !important; }
.m-8 { margin: 2rem !important; }
.m-10 { margin: 2.5rem !important; }
.m-12 { margin: 3rem !important; }
.m-16 { margin: 4rem !important; }
.m-20 { margin: 5rem !important; }
.m-auto { margin: auto !important; }

/* Margin Top */
.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 0.75rem !important; }
.mt-4 { margin-top: 1rem !important; }
.mt-5 { margin-top: 1.25rem !important; }
.mt-6 { margin-top: 1.5rem !important; }
.mt-8 { margin-top: 2rem !important; }
.mt-10 { margin-top: 2.5rem !important; }
.mt-12 { margin-top: 3rem !important; }
.mt-16 { margin-top: 4rem !important; }
.mt-20 { margin-top: 5rem !important; }
.mt-auto { margin-top: auto !important; }

/* Margin Bottom */
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 0.75rem !important; }
.mb-4 { margin-bottom: 1rem !important; }
.mb-5 { margin-bottom: 1.25rem !important; }
.mb-6 { margin-bottom: 1.5rem !important; }
.mb-8 { margin-bottom: 2rem !important; }
.mb-10 { margin-bottom: 2.5rem !important; }
.mb-12 { margin-bottom: 3rem !important; }
.mb-16 { margin-bottom: 4rem !important; }
.mb-20 { margin-bottom: 5rem !important; }
.mb-auto { margin-bottom: auto !important; }

/* Margin Left & Right */
.ml-0, .mr-0 { margin-left: 0 !important; margin-right: 0 !important; }
.ml-1, .mr-1 { margin-left: 0.25rem !important; margin-right: 0.25rem !important; }
.ml-2, .mr-2 { margin-left: 0.5rem !important; margin-right: 0.5rem !important; }
.ml-3, .mr-3 { margin-left: 0.75rem !important; margin-right: 0.75rem !important; }
.ml-4, .mr-4 { margin-left: 1rem !important; margin-right: 1rem !important; }
.ml-6, .mr-6 { margin-left: 1.5rem !important; margin-right: 1.5rem !important; }
.ml-8, .mr-8 { margin-left: 2rem !important; margin-right: 2rem !important; }
.ml-auto, .mr-auto { margin-left: auto !important; margin-right: auto !important; }

/* Padding */
.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 0.75rem !important; }
.p-4 { padding: 1rem !important; }
.p-5 { padding: 1.25rem !important; }
.p-6 { padding: 1.5rem !important; }
.p-8 { padding: 2rem !important; }
.p-10 { padding: 2.5rem !important; }
.p-12 { padding: 3rem !important; }
.p-16 { padding: 4rem !important; }
.p-20 { padding: 5rem !important; }

/* Padding Top */
.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: 0.25rem !important; }
.pt-2 { padding-top: 0.5rem !important; }
.pt-3 { padding-top: 0.75rem !important; }
.pt-4 { padding-top: 1rem !important; }
.pt-5 { padding-top: 1.25rem !important; }
.pt-6 { padding-top: 1.5rem !important; }
.pt-8 { padding-top: 2rem !important; }
.pt-10 { padding-top: 2.5rem !important; }
.pt-12 { padding-top: 3rem !important; }
.pt-16 { padding-top: 4rem !important; }
.pt-20 { padding-top: 5rem !important; }

/* Padding Bottom */
.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: 0.25rem !important; }
.pb-2 { padding-bottom: 0.5rem !important; }
.pb-3 { padding-bottom: 0.75rem !important; }
.pb-4 { padding-bottom: 1rem !important; }
.pb-5 { padding-bottom: 1.25rem !important; }
.pb-6 { padding-bottom: 1.5rem !important; }
.pb-8 { padding-bottom: 2rem !important; }
.pb-10 { padding-bottom: 2.5rem !important; }
.pb-12 { padding-bottom: 3rem !important; }
.pb-16 { padding-bottom: 4rem !important; }
.pb-20 { padding-bottom: 5rem !important; }

/* ===== فئات النصوص الاحترافية ===== */
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }
.text-3xl { font-size: var(--font-size-3xl) !important; }
.text-4xl { font-size: var(--font-size-4xl) !important; }
.text-5xl { font-size: var(--font-size-5xl) !important; }
.text-6xl { font-size: var(--font-size-6xl) !important; }

.font-thin { font-weight: 100 !important; }
.font-light { font-weight: 300 !important; }
.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }
.font-extrabold { font-weight: 800 !important; }
.font-black { font-weight: 900 !important; }

.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-tertiary { color: var(--text-tertiary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-blue { color: var(--primary-600) !important; }
.text-white { color: white !important; }

.leading-none { line-height: 1 !important; }
.leading-tight { line-height: 1.25 !important; }
.leading-snug { line-height: 1.375 !important; }
.leading-normal { line-height: 1.5 !important; }
.leading-relaxed { line-height: 1.625 !important; }
.leading-loose { line-height: 2 !important; }

/* ===== فئات الخلفيات والحدود ===== */
.bg-primary { background-color: var(--bg-primary) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-tertiary { background-color: var(--bg-tertiary) !important; }
.bg-accent { background-color: var(--bg-accent) !important; }
.bg-blue { background-color: var(--primary-600) !important; }
.bg-transparent { background-color: transparent !important; }

.border { border: 1px solid var(--border-color) !important; }
.border-0 { border: 0 !important; }
.border-2 { border: 2px solid var(--border-color) !important; }
.border-t { border-top: 1px solid var(--border-color) !important; }
.border-b { border-bottom: 1px solid var(--border-color) !important; }
.border-l { border-left: 1px solid var(--border-color) !important; }
.border-r { border-right: 1px solid var(--border-color) !important; }

.rounded-none { border-radius: 0 !important; }
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded { border-radius: var(--border-radius) !important; }
.rounded-md { border-radius: var(--border-radius-md) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-2xl { border-radius: var(--border-radius-2xl) !important; }
.rounded-full { border-radius: var(--border-radius-full) !important; }

/* ===== فئات الظلال ===== */
.shadow-none { box-shadow: none !important; }
.shadow-xs { box-shadow: var(--shadow-xs) !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-2xl { box-shadow: var(--shadow-2xl) !important; }

/* ===== فئات الموضع ===== */
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }
.static { position: static !important; }

.top-0 { top: 0 !important; }
.right-0 { right: 0 !important; }
.bottom-0 { bottom: 0 !important; }
.left-0 { left: 0 !important; }

.z-0 { z-index: 0 !important; }
.z-10 { z-index: 10 !important; }
.z-20 { z-index: 20 !important; }
.z-30 { z-index: 30 !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }

/* ===== فئات العرض والارتفاع ===== */
.w-auto { width: auto !important; }
.w-full { width: 100% !important; }
.w-screen { width: 100vw !important; }
.w-1\/2 { width: 50% !important; }
.w-1\/3 { width: 33.333333% !important; }
.w-2\/3 { width: 66.666667% !important; }
.w-1\/4 { width: 25% !important; }
.w-3\/4 { width: 75% !important; }

.h-auto { height: auto !important; }
.h-full { height: 100% !important; }
.h-screen { height: 100vh !important; }
.h-1\/2 { height: 50% !important; }
.h-1\/3 { height: 33.333333% !important; }
.h-2\/3 { height: 66.666667% !important; }
.h-1\/4 { height: 25% !important; }
.h-3\/4 { height: 75% !important; }

/* ===== فئات التفاعل ===== */
.cursor-auto { cursor: auto !important; }
.cursor-default { cursor: default !important; }
.cursor-pointer { cursor: pointer !important; }
.cursor-wait { cursor: wait !important; }
.cursor-text { cursor: text !important; }
.cursor-move { cursor: move !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

.select-none { user-select: none !important; }
.select-text { user-select: text !important; }
.select-all { user-select: all !important; }
.select-auto { user-select: auto !important; }

.pointer-events-none { pointer-events: none !important; }
.pointer-events-auto { pointer-events: auto !important; }

/* ===== فئات الشفافية ===== */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* ===== فئات التحويل ===== */
.transform { transform: translateZ(0) !important; }
.transform-none { transform: none !important; }
.scale-0 { transform: scale(0) !important; }
.scale-50 { transform: scale(0.5) !important; }
.scale-75 { transform: scale(0.75) !important; }
.scale-90 { transform: scale(0.9) !important; }
.scale-95 { transform: scale(0.95) !important; }
.scale-100 { transform: scale(1) !important; }
.scale-105 { transform: scale(1.05) !important; }
.scale-110 { transform: scale(1.1) !important; }
.scale-125 { transform: scale(1.25) !important; }
.scale-150 { transform: scale(1.5) !important; }

/* ===== فئات الانتقال ===== */
.transition-none { transition: none !important; }
.transition-all { transition: all 0.15s ease-in-out !important; }
.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out !important; }
.transition-opacity { transition: opacity 0.15s ease-in-out !important; }
.transition-shadow { transition: box-shadow 0.15s ease-in-out !important; }
.transition-transform { transition: transform 0.15s ease-in-out !important; }

.duration-75 { transition-duration: 75ms !important; }
.duration-100 { transition-duration: 100ms !important; }
.duration-150 { transition-duration: 150ms !important; }
.duration-200 { transition-duration: 200ms !important; }
.duration-300 { transition-duration: 300ms !important; }
.duration-500 { transition-duration: 500ms !important; }
.duration-700 { transition-duration: 700ms !important; }
.duration-1000 { transition-duration: 1000ms !important; }
