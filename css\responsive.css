/* Responsive Design */

/* Extra Large Screens (1400px and up) - شاشات كبيرة جداً */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
        padding: 0 2.5rem;
    }

    /* Hero Section للشاشات الكبيرة جداً */
    .hero {
        padding: 10rem 0 8rem;
        min-height: 100vh;
    }

    .hero-content {
        gap: 10rem;
        align-items: center;
    }

    .hero-title {
        font-size: var(--font-size-8xl);
        line-height: 1.05;
        margin-bottom: 2.5rem;
        letter-spacing: -0.02em;
    }

    .hero-subtitle {
        font-size: var(--font-size-2xl);
        line-height: 1.7;
        margin-bottom: 4rem;
        max-width: 600px;
    }

    .hero-buttons {
        gap: 2.5rem;
        margin-bottom: 4rem;
    }

    .hero-buttons .btn {
        padding: 1.5rem 3.5rem;
        font-size: var(--font-size-xl);
        min-height: 64px;
        border-radius: var(--border-radius-lg);
    }

    .hero-image img {
        height: 700px;
        border-radius: var(--border-radius-2xl);
        object-fit: cover;
    }

    .hero-stats {
        gap: 4rem;
        margin-top: 4rem;
    }

    .stat {
        padding: 2.5rem 2rem;
        min-width: 200px;
        border-radius: var(--border-radius-xl);
    }

    .stat-number {
        font-size: var(--font-size-6xl);
        margin-bottom: 1.25rem;
        font-weight: 800;
    }

    .stat-label {
        font-size: var(--font-size-lg);
        font-weight: 500;
    }

    /* Sections للشاشات الكبيرة جداً */
    .section {
        padding: 8rem 0;
    }

    .section-title {
        font-size: var(--font-size-6xl);
        margin-bottom: 2rem;
        line-height: 1.1;
    }

    .section-subtitle {
        font-size: var(--font-size-xl);
        margin-bottom: 4rem;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Grids للشاشات الكبيرة جداً */
    .services-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 3.5rem;
    }

    .products-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 3.5rem;
    }

    .works-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 3.5rem;
    }

    /* Cards للشاشات الكبيرة جداً */
    .service-card,
    .product-card,
    .work-card {
        padding: 3.5rem 2.5rem;
        border-radius: var(--border-radius-2xl);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .service-card:hover,
    .product-card:hover,
    .work-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .service-card .icon-container {
        width: 120px;
        height: 120px;
        margin-bottom: 2.5rem;
        border-radius: var(--border-radius-lg);
    }

    .service-card .icon-container i {
        font-size: var(--font-size-5xl);
    }

    .service-title,
    .product-title,
    .work-title {
        font-size: var(--font-size-2xl);
        margin-bottom: 1.75rem;
        font-weight: 700;
    }

    .service-description,
    .product-description,
    .work-description {
        font-size: var(--font-size-lg);
        line-height: 1.8;
    }

    .product-image,
    .work-image {
        height: 280px;
        border-radius: var(--border-radius-lg);
    }

    .product-price {
        font-size: var(--font-size-3xl);
        margin: 1.5rem 0;
        font-weight: 800;
    }

    /* About Section للشاشات الكبيرة جداً */
    .about-content {
        gap: 8rem;
        align-items: center;
    }

    .about-features {
        gap: 2.5rem;
    }

    .feature {
        padding: 2.5rem;
        border-radius: var(--border-radius-xl);
    }

    .feature-icon {
        width: 100px;
        height: 100px;
        margin-bottom: 2rem;
        border-radius: var(--border-radius-lg);
    }

    .feature-icon i {
        font-size: var(--font-size-4xl);
    }

    .feature-content h3 {
        font-size: var(--font-size-xl);
        margin-bottom: 1rem;
    }

    .feature-content p {
        font-size: var(--font-size-lg);
        line-height: 1.7;
    }

    /* Contact Section للشاشات الكبيرة جداً */
    .contact-content {
        gap: 8rem;
    }

    .contact-info {
        gap: 2.5rem;
    }

    .contact-item {
        padding: 2.5rem;
        border-radius: var(--border-radius-xl);
    }

    .contact-icon {
        width: 100px;
        height: 100px;
        margin-bottom: 2rem;
        border-radius: var(--border-radius-lg);
    }

    .contact-icon i {
        font-size: var(--font-size-4xl);
    }

    .contact-details h3 {
        font-size: var(--font-size-xl);
        margin-bottom: 1rem;
    }

    .contact-details p {
        font-size: var(--font-size-lg);
    }

    .contact-form {
        padding: 3rem;
        border-radius: var(--border-radius-xl);
    }

    .form-group {
        margin-bottom: 2rem;
    }

    .form-group label {
        font-size: var(--font-size-lg);
        margin-bottom: 1rem;
    }

    .form-group input,
    .form-group textarea {
        padding: 1.25rem 1.5rem;
        font-size: var(--font-size-base);
        border-radius: var(--border-radius-lg);
    }

    /* Footer للشاشات الكبيرة جداً */
    .footer {
        padding: 8rem 0 4rem;
    }

    .footer-content {
        gap: 5rem;
    }

    .footer-section {
        padding: 2.5rem 0;
    }

    .footer-title {
        font-size: var(--font-size-2xl);
        margin-bottom: 2rem;
    }

    .footer-links a {
        font-size: var(--font-size-lg);
        padding: 0.75rem 0;
    }

    .social-link {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
        border-radius: var(--border-radius-lg);
    }
}

/* Large Screens (1200px and up) - شاشات كبيرة */
@media (min-width: 1200px) and (max-width: 1399px) {
    .container {
        max-width: 1200px;
        padding: 0 2rem;
    }

    /* Hero Section للشاشات الكبيرة */
    .hero {
        padding: 8rem 0 6rem;
        min-height: 95vh;
    }

    .hero-content {
        gap: 7rem;
        align-items: center;
    }

    .hero-title {
        font-size: var(--font-size-7xl);
        line-height: 1.1;
        margin-bottom: 2rem;
        letter-spacing: -0.015em;
    }

    .hero-subtitle {
        font-size: var(--font-size-xl);
        line-height: 1.65;
        margin-bottom: 3rem;
        max-width: 550px;
    }

    .hero-buttons {
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .hero-buttons .btn {
        padding: 1.25rem 3rem;
        font-size: var(--font-size-lg);
        min-height: 56px;
        border-radius: var(--border-radius-lg);
    }

    .hero-image img {
        height: 600px;
        border-radius: var(--border-radius-xl);
        object-fit: cover;
    }

    .hero-stats {
        gap: 3rem;
        margin-top: 3rem;
    }

    .stat {
        padding: 2rem 1.5rem;
        min-width: 180px;
        border-radius: var(--border-radius-lg);
    }

    .stat-number {
        font-size: var(--font-size-5xl);
        margin-bottom: 1rem;
        font-weight: 800;
    }

    .stat-label {
        font-size: var(--font-size-base);
        font-weight: 500;
    }

    /* Sections للشاشات الكبيرة */
    .section {
        padding: 6rem 0;
    }

    .section-title {
        font-size: var(--font-size-5xl);
        margin-bottom: 1.5rem;
        line-height: 1.15;
    }

    .section-subtitle {
        font-size: var(--font-size-lg);
        margin-bottom: 3rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Grids للشاشات الكبيرة */
    .services-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 3rem;
    }

    .products-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 3rem;
    }

    .works-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 3rem;
    }

    /* Cards للشاشات الكبيرة */
    .service-card,
    .product-card,
    .work-card {
        padding: 3rem 2.25rem;
        border-radius: var(--border-radius-xl);
        transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .service-card:hover,
    .product-card:hover,
    .work-card:hover {
        transform: translateY(-6px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
    }

    .service-card .icon-container {
        width: 100px;
        height: 100px;
        margin-bottom: 2rem;
        border-radius: var(--border-radius-lg);
    }

    .service-card .icon-container i {
        font-size: var(--font-size-4xl);
    }

    .service-title,
    .product-title,
    .work-title {
        font-size: var(--font-size-xl);
        margin-bottom: 1.25rem;
        font-weight: 700;
    }

    .service-description,
    .product-description,
    .work-description {
        font-size: var(--font-size-base);
        line-height: 1.7;
    }

    .product-image,
    .work-image {
        height: 240px;
        border-radius: var(--border-radius-lg);
    }

    .product-price {
        font-size: var(--font-size-2xl);
        margin: 1.25rem 0;
        font-weight: 800;
    }

    /* About Section للشاشات الكبيرة */
    .about-content {
        gap: 6rem;
        align-items: center;
    }

    .about-features {
        gap: 2rem;
    }

    .feature {
        padding: 2rem;
        border-radius: var(--border-radius-lg);
    }

    .feature-icon {
        width: 80px;
        height: 80px;
        margin-bottom: 1.5rem;
        border-radius: var(--border-radius);
    }

    .feature-icon i {
        font-size: var(--font-size-3xl);
    }

    .feature-content h3 {
        font-size: var(--font-size-lg);
        margin-bottom: 0.75rem;
    }

    .feature-content p {
        font-size: var(--font-size-base);
        line-height: 1.6;
    }

    /* Contact Section للشاشات الكبيرة */
    .contact-content {
        gap: 6rem;
    }

    .contact-info {
        gap: 2rem;
    }

    .contact-item {
        padding: 2rem;
        border-radius: var(--border-radius-lg);
    }

    .contact-icon {
        width: 80px;
        height: 80px;
        margin-bottom: 1.5rem;
        border-radius: var(--border-radius);
    }

    .contact-icon i {
        font-size: var(--font-size-3xl);
    }

    .contact-details h3 {
        font-size: var(--font-size-lg);
        margin-bottom: 0.75rem;
    }

    .contact-details p {
        font-size: var(--font-size-base);
    }

    .contact-form {
        padding: 2.5rem;
        border-radius: var(--border-radius-lg);
    }

    .form-group {
        margin-bottom: 1.75rem;
    }

    .form-group label {
        font-size: var(--font-size-base);
        margin-bottom: 0.75rem;
    }

    .form-group input,
    .form-group textarea {
        padding: 1rem 1.25rem;
        font-size: var(--font-size-base);
        border-radius: var(--border-radius);
    }

    /* Footer للشاشات الكبيرة */
    .footer {
        padding: 6rem 0 3rem;
    }

    .footer-content {
        gap: 4rem;
    }

    .footer-section {
        padding: 2rem 0;
    }

    .footer-title {
        font-size: var(--font-size-xl);
        margin-bottom: 1.5rem;
    }

    .footer-links a {
        font-size: var(--font-size-base);
        padding: 0.5rem 0;
    }

    .social-link {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
        border-radius: var(--border-radius);
    }
}

/* Medium Screens (768px to 1199px) - شاشات متوسطة */
@media (min-width: 768px) and (max-width: 1199px) {
    .container {
        max-width: 1024px;
        padding: 0 1.75rem;
    }

    /* Hero Section للشاشات المتوسطة */
    .hero {
        padding: 6rem 0 5rem;
        min-height: 90vh;
    }

    .hero-content {
        gap: 5rem;
        align-items: center;
    }

    .hero-title {
        font-size: var(--font-size-6xl);
        line-height: 1.15;
        margin-bottom: 1.75rem;
        letter-spacing: -0.01em;
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
        line-height: 1.6;
        margin-bottom: 2.5rem;
        max-width: 500px;
    }

    .hero-buttons {
        gap: 1.75rem;
        margin-bottom: 2.5rem;
    }

    .hero-buttons .btn {
        padding: 1.125rem 2.5rem;
        font-size: var(--font-size-base);
        min-height: 52px;
        border-radius: var(--border-radius);
    }

    .hero-image img {
        height: 500px;
        border-radius: var(--border-radius-lg);
        object-fit: cover;
    }

    .hero-stats {
        gap: 2.5rem;
        margin-top: 2.5rem;
    }

    .stat {
        padding: 1.5rem 1.25rem;
        min-width: 160px;
        border-radius: var(--border-radius);
    }

    .stat-number {
        font-size: var(--font-size-4xl);
        margin-bottom: 0.75rem;
        font-weight: 800;
    }

    .stat-label {
        font-size: var(--font-size-sm);
        font-weight: 500;
    }

    /* Sections للشاشات المتوسطة */
    .section {
        padding: 5rem 0;
    }

    .section-title {
        font-size: var(--font-size-4xl);
        margin-bottom: 1.25rem;
        line-height: 1.2;
    }

    .section-subtitle {
        font-size: var(--font-size-base);
        margin-bottom: 2.5rem;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Grids للشاشات المتوسطة */
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2.5rem;
    }

    .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2.5rem;
    }

    .works-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2.5rem;
    }

    /* Cards للشاشات المتوسطة */
    .service-card,
    .product-card,
    .work-card {
        padding: 2.5rem 2rem;
        border-radius: var(--border-radius-lg);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .service-card:hover,
    .product-card:hover,
    .work-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }

    .service-card .icon-container {
        width: 80px;
        height: 80px;
        margin-bottom: 1.5rem;
        border-radius: var(--border-radius);
    }

    .service-card .icon-container i {
        font-size: var(--font-size-3xl);
    }

    .service-title,
    .product-title,
    .work-title {
        font-size: var(--font-size-lg);
        margin-bottom: 1rem;
        font-weight: 700;
    }

    .service-description,
    .product-description,
    .work-description {
        font-size: var(--font-size-sm);
        line-height: 1.65;
    }

    .product-image,
    .work-image {
        height: 200px;
        border-radius: var(--border-radius);
    }

    .product-price {
        font-size: var(--font-size-xl);
        margin: 1rem 0;
        font-weight: 800;
    }

    /* About Section للشاشات المتوسطة */
    .about-content {
        gap: 5rem;
        align-items: center;
    }

    .about-features {
        gap: 2rem;
    }

    .feature {
        padding: 1.75rem;
        border-radius: var(--border-radius);
    }

    .feature-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 1.25rem;
        border-radius: var(--border-radius-sm);
    }

    .feature-icon i {
        font-size: var(--font-size-2xl);
    }

    .feature-content h3 {
        font-size: var(--font-size-base);
        margin-bottom: 0.75rem;
    }

    .feature-content p {
        font-size: var(--font-size-sm);
        line-height: 1.6;
    }

    /* Contact Section للشاشات المتوسطة */
    .contact-content {
        gap: 5rem;
    }

    .contact-info {
        gap: 2rem;
    }

    .contact-item {
        padding: 1.75rem;
        border-radius: var(--border-radius);
    }

    .contact-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 1.25rem;
        border-radius: var(--border-radius-sm);
    }

    .contact-icon i {
        font-size: var(--font-size-2xl);
    }

    .contact-details h3 {
        font-size: var(--font-size-base);
        margin-bottom: 0.75rem;
    }

    .contact-details p {
        font-size: var(--font-size-sm);
    }

    .contact-form {
        padding: 2rem;
        border-radius: var(--border-radius);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        font-size: var(--font-size-sm);
        margin-bottom: 0.5rem;
    }

    .form-group input,
    .form-group textarea {
        padding: 0.875rem 1rem;
        font-size: var(--font-size-sm);
        border-radius: var(--border-radius-sm);
    }

    /* Footer للشاشات المتوسطة */
    .footer {
        padding: 5rem 0 2.5rem;
    }

    .footer-content {
        gap: 3rem;
    }

    .footer-section {
        padding: 1.5rem 0;
    }

    .footer-title {
        font-size: var(--font-size-lg);
        margin-bottom: 1.25rem;
    }

    .footer-links a {
        font-size: var(--font-size-sm);
        padding: 0.375rem 0;
    }

    .social-link {
        width: 45px;
        height: 45px;
        font-size: var(--font-size-base);
        border-radius: var(--border-radius-sm);
    }
}

/* Tablet Screens (768px to 991px) - أجهزة لوحية */
@media (min-width: 768px) and (max-width: 991px) {
    :root {
        --section-padding: 4rem 0;
    }

    .container {
        max-width: 750px;
        padding: 0 1.25rem;
    }

    /* Hero Section للأجهزة اللوحية */
    .hero {
        padding: 4rem 0 3rem;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
        line-height: 1.2;
        margin-bottom: 1.25rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    .hero-buttons {
        gap: 1.25rem;
        margin-bottom: 2rem;
        justify-content: center;
    }

    .hero-buttons .btn {
        padding: 0.875rem 1.75rem;
        font-size: 1rem;
        min-width: 160px;
    }

    .hero-image {
        order: -1;
        max-width: 450px;
        margin: 0 auto;
    }

    .hero-image img {
        height: 380px;
        border-radius: 18px;
    }

    .hero-stats {
        justify-content: center;
        max-width: 600px;
        margin: 2rem auto 0;
        gap: 1.5rem;
    }

    .stat {
        padding: 1rem;
        min-width: 120px;
    }

    .stat-number {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 0.9rem;
    }

    .floating-card {
        position: static;
        margin-top: 1.5rem;
        justify-content: center;
        max-width: 250px;
        margin-left: auto;
        margin-right: auto;
    }

    /* About Section للأجهزة اللوحية */
    .about-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .about-image {
        order: -1;
        max-width: 400px;
        margin: 0 auto;
    }

    .about-image img {
        border-radius: 16px;
    }

    .about-features {
        gap: 1.25rem;
    }

    .feature {
        padding: 1.25rem;
        border-radius: 12px;
        text-align: center;
        max-width: 300px;
        margin: 0 auto;
    }

    .feature-icon {
        width: 55px;
        height: 55px;
        margin: 0 auto 1rem;
    }

    .feature-icon i {
        font-size: 1.5rem;
    }

    /* Contact Section للأجهزة اللوحية */
    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .contact-info {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.25rem;
    }

    .contact-item {
        padding: 1.25rem;
        border-radius: 12px;
        text-align: center;
    }

    .contact-icon {
        width: 55px;
        height: 55px;
        margin: 0 auto 1rem;
    }

    .contact-icon i {
        font-size: 1.5rem;
    }

    .contact-form {
        padding: 1.5rem;
        border-radius: 16px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Grids للأجهزة اللوحية */
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.75rem;
    }

    .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.75rem;
    }

    .works-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.75rem;
    }

    /* Cards للأجهزة اللوحية */
    .service-card,
    .product-card,
    .work-card {
        padding: 1.75rem 1.25rem;
        border-radius: 14px;
        margin: 0 auto;
        max-width: 350px;
    }

    .service-card .icon-container {
        width: 65px;
        height: 65px;
        margin-bottom: 1rem;
    }

    .service-card .icon-container i {
        font-size: 2rem;
    }

    .service-title,
    .product-title,
    .work-title {
        font-size: 1.15rem;
        margin-bottom: 0.875rem;
    }

    .service-description,
    .product-description,
    .work-description {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    /* Footer للأجهزة اللوحية */
    .footer {
        padding: 3.5rem 0 2rem;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
        text-align: center;
    }

    .footer-section {
        padding: 1rem 0;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .social-links {
        justify-content: center;
        gap: 1rem;
    }

    .social-link {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

/* Mobile Screens (768px and below) */
@media (max-width: 768px) {
    /* Navigation */
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--bg-primary);
        border-top: 1px solid var(--border-color);
        flex-direction: column;
        padding: 2rem;
        gap: 1rem;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
        z-index: 1000;
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .mobile-toggle {
        display: flex;
    }

    .mobile-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .mobile-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .mobile-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .nav-controls {
        gap: 0.5rem;
    }

    .lang-menu {
        right: auto;
        left: 0;
    }

    /* Hero Section للهواتف */
    .hero {
        min-height: 85vh;
        padding: 4rem 0 3rem;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 2.5rem;
        text-align: center;
    }

    .hero-title {
        font-size: var(--font-size-4xl);
        line-height: 1.15;
        margin-bottom: 1.25rem;
        letter-spacing: -0.01em;
        font-weight: 800;
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
        line-height: 1.65;
        margin-bottom: 2rem;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 1.25rem;
        margin-bottom: 2rem;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 320px;
        padding: 1.125rem 2rem;
        font-size: var(--font-size-base);
        min-height: 52px;
        justify-content: center;
        border-radius: var(--border-radius);
        font-weight: 600;
    }

    .hero-image {
        order: -1;
        max-width: 320px;
        margin: 0 auto;
    }

    .hero-image img {
        height: 300px;
        border-radius: var(--border-radius-lg);
        object-fit: cover;
    }

    .hero-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.25rem;
        max-width: none;
        margin-top: 2rem;
    }

    .stat {
        padding: 1.25rem 0.75rem;
        min-width: auto;
        border-radius: var(--border-radius);
    }

    .stat-number {
        font-size: var(--font-size-2xl);
        margin-bottom: 0.5rem;
        font-weight: 800;
    }

    .stat-label {
        font-size: var(--font-size-xs);
        font-weight: 500;
    }

    .floating-card {
        position: static;
        margin-top: 1rem;
        justify-content: center;
    }

    /* Sections */
    :root {
        --section-padding: 40px 0;
    }

    .section-header {
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    /* Grids */
    .services-grid,
    .products-grid,
    .works-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    /* Sections للهواتف */
    .section {
        padding: 4rem 0;
    }

    .section-title {
        font-size: var(--font-size-3xl);
        margin-bottom: 1rem;
        line-height: 1.25;
        font-weight: 800;
    }

    .section-subtitle {
        font-size: var(--font-size-sm);
        margin-bottom: 2rem;
        max-width: 350px;
        margin-left: auto;
        margin-right: auto;
        line-height: 1.6;
    }

    /* Cards للهواتف */
    .service-card,
    .product-card,
    .work-card {
        margin: 0 auto;
        max-width: 350px;
        padding: 2rem 1.5rem;
        border-radius: var(--border-radius-lg);
        text-align: center;
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .service-card:active,
    .product-card:active,
    .work-card:active {
        transform: scale(0.98);
    }

    .service-card .icon-container {
        width: 70px;
        height: 70px;
        margin-bottom: 1.25rem;
        border-radius: var(--border-radius);
    }

    .service-card .icon-container i {
        font-size: var(--font-size-2xl);
    }

    .service-title,
    .product-title,
    .work-title {
        font-size: var(--font-size-lg);
        margin-bottom: 1rem;
        font-weight: 700;
    }

    .service-description,
    .product-description,
    .work-description {
        font-size: var(--font-size-sm);
        line-height: 1.6;
    }

    .product-image,
    .work-image {
        height: 180px;
        border-radius: var(--border-radius);
    }

    .product-price {
        font-size: var(--font-size-xl);
        margin: 1rem 0;
        font-weight: 800;
    }

    /* About Section للهواتف */
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .about-image {
        order: -1;
        max-width: 280px;
        margin: 0 auto;
    }

    .about-image img {
        border-radius: 14px;
    }

    .about-features {
        gap: 1rem;
    }

    .feature {
        padding: 1rem;
        border-radius: 12px;
        text-align: center;
        max-width: 260px;
        margin: 0 auto;
    }

    .feature-icon {
        width: 45px;
        height: 45px;
        margin: 0 auto 0.75rem;
    }

    .feature-icon i {
        font-size: 1.2rem;
    }

    /* Contact Section للهواتف */
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .contact-item {
        padding: 1rem;
        border-radius: 12px;
        text-align: center;
    }

    .contact-icon {
        width: 45px;
        height: 45px;
        margin: 0 auto 0.75rem;
    }

    .contact-icon i {
        font-size: 1.2rem;
    }

    .contact-details h3 {
        font-size: 0.95rem;
        margin-bottom: 0.5rem;
    }

    .contact-details p {
        font-size: 0.85rem;
    }

    .contact-form {
        padding: 1.25rem;
        border-radius: 14px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-group label {
        font-size: 0.95rem;
        margin-bottom: 0.5rem;
    }

    .form-group input,
    .form-group textarea {
        padding: 0.875rem 1rem;
        font-size: 0.95rem;
        border-radius: 8px;
    }

    /* Footer للهواتف */
    .footer {
        padding: 2.5rem 0 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-section {
        padding: 1rem 0;
    }

    .footer-title {
        font-size: 1.05rem;
        margin-bottom: 1rem;
    }

    .footer-links {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.75rem;
    }

    .footer-links a {
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        font-size: 0.9rem;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .social-links {
        justify-content: center;
        gap: 0.75rem;
    }

    .social-link {
        width: 38px;
        height: 38px;
        font-size: 0.95rem;
        border-radius: 8px;
    }

    /* Modal للهواتف */
    .modal-content {
        width: 95%;
        margin: 1rem;
        border-radius: 12px;
        max-height: calc(100vh - 2rem);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }

    .modal-footer {
        flex-direction: column;
        gap: 1rem;
    }

    .cart-total {
        text-align: center;
        font-size: 1.05rem;
    }

    /* Cart Items للهواتف */
    .cart-item {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
        padding: 1rem;
        border-radius: 8px;
    }

    .cart-item-image {
        align-self: center;
        width: 55px;
        height: 55px;
        border-radius: 6px;
    }

    .cart-item-details h4 {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .cart-item-price {
        font-size: 0.95rem;
        font-weight: 700;
    }

    .cart-item-actions {
        justify-content: center;
        gap: 0.5rem;
    }

    .quantity-controls button {
        width: 30px;
        height: 30px;
        font-size: 0.85rem;
    }

    /* Touch optimizations للهواتف */
    .cart-btn,
    .theme-toggle,
    .lang-btn {
        min-width: 42px;
        min-height: 42px;
        border-radius: 8px;
        touch-action: manipulation;
    }

    .add-to-cart {
        width: 100%;
        padding: 0.875rem;
        font-size: 0.9rem;
        min-height: 42px;
        border-radius: 8px;
    }
}

/* Small Mobile Screens (480px and below) - هواتف صغيرة */
@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    /* Header للهواتف الصغيرة */
    .navbar {
        padding: 1rem 0;
        min-height: 60px;
    }

    .logo-text {
        font-size: var(--font-size-xl);
        font-weight: 700;
    }

    .logo-icon {
        width: 36px;
        height: 36px;
        font-size: var(--font-size-base);
    }

    /* Hero للهواتف الصغيرة */
    .hero {
        padding: 3rem 0 2rem;
        min-height: 80vh;
    }

    .hero-title {
        font-size: var(--font-size-3xl);
        line-height: 1.2;
        margin-bottom: 1rem;
        font-weight: 800;
        letter-spacing: -0.01em;
    }

    .hero-subtitle {
        font-size: var(--font-size-base);
        line-height: 1.6;
        margin-bottom: 1.5rem;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .hero-buttons .btn {
        width: 100%;
        padding: 1rem 1.5rem;
        font-size: var(--font-size-sm);
        font-weight: 600;
        border-radius: var(--border-radius);
        min-height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .hero-image {
        max-width: 260px;
    }

    .hero-image img {
        height: 240px;
        border-radius: var(--border-radius-lg);
        object-fit: cover;
    }

    .hero-stats {
        margin-top: 1.5rem;
        gap: 0.75rem;
    }

    .stat {
        padding: 1rem 0.5rem;
        background: rgba(255, 255, 255, 0.15);
        border-radius: var(--border-radius);
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stat-number {
        font-size: var(--font-size-xl);
        font-weight: 800;
        margin-bottom: 0.25rem;
    }

    .stat-label {
        font-size: var(--font-size-xs);
        opacity: 0.9;
    }

    /* Sections للهواتف الصغيرة */
    .section {
        padding: 3rem 0;
    }

    .section-title {
        font-size: var(--font-size-2xl);
        margin-bottom: 0.75rem;
        line-height: 1.3;
    }

    .section-subtitle {
        font-size: var(--font-size-xs);
        margin-bottom: 1.5rem;
        max-width: 280px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Buttons محسنة للهواتف */
    .btn {
        padding: 1rem 2rem;
        font-size: 1rem;
        font-weight: 600;
        border-radius: 12px;
        min-height: 48px;
        touch-action: manipulation;
        transition: all 0.3s ease;
    }

    .btn:active {
        transform: scale(0.98);
    }

    /* Cards محسنة للهواتف */
    .service-card,
    .product-card,
    .work-card {
        padding: 2rem 1.5rem;
        border-radius: 20px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .service-card:active,
    .product-card:active,
    .work-card:active {
        transform: scale(0.98);
    }

    .service-icon,
    .icon-container i {
        font-size: 3rem;
        margin-bottom: 1.5rem;
    }

    .service-title,
    .product-title,
    .work-title {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .service-description,
    .product-description,
    .work-description {
        font-size: 1rem;
        line-height: 1.6;
        opacity: 0.8;
    }

    .product-image,
    .work-image {
        height: 200px;
        border-radius: 16px;
        object-fit: cover;
    }

    .product-price {
        font-size: 1.5rem;
        font-weight: 800;
        color: var(--primary-600);
        margin: 1rem 0;
    }

    /* Features محسنة للهواتف */
    .feature {
        flex-direction: row;
        text-align: right;
        gap: 1rem;
        padding: 1.5rem;
        background: var(--bg-secondary);
        border-radius: 16px;
        border: 1px solid var(--border-color-light);
    }

    [dir="ltr"] .feature {
        text-align: left;
    }

    .feature-icon {
        flex-shrink: 0;
        width: 60px;
        height: 60px;
        background: var(--primary-100);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
    }

    .feature-icon i {
        font-size: 1.5rem;
        color: var(--primary-600);
    }

    .feature-content h3 {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .feature-content p {
        font-size: 1rem;
        line-height: 1.5;
        opacity: 0.8;
    }

    /* Contact محسن للهواتف */
    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
        padding: 2rem 1.5rem;
        background: var(--bg-secondary);
        border-radius: 16px;
        border: 1px solid var(--border-color-light);
    }

    .contact-icon {
        width: 60px;
        height: 60px;
        background: var(--primary-100);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
    }

    .contact-icon i {
        font-size: 1.5rem;
        color: var(--primary-600);
    }

    .contact-details h3 {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .contact-details p {
        font-size: 1rem;
        font-weight: 600;
        color: var(--primary-600);
    }

    /* Form محسن للهواتف */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.75rem;
        display: block;
    }

    .form-group input,
    .form-group textarea {
        padding: 1rem 1.25rem;
        font-size: 1rem;
        border-radius: 12px;
        border: 2px solid var(--border-color);
        background: var(--bg-primary);
        width: 100%;
        transition: all 0.3s ease;
    }

    .form-group input:focus,
    .form-group textarea:focus {
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        outline: none;
    }

    /* Footer محسن للهواتف */
    .footer {
        padding: 3rem 0 2rem;
    }

    .footer-content {
        gap: 2rem;
    }

    .footer-section {
        text-align: center;
        padding: 1.5rem 0;
    }

    .footer-title {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
    }

    .footer-links {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }

    .footer-links li {
        margin: 0;
    }

    .footer-links a {
        padding: 0.5rem 1rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .footer-links a:hover {
        background: var(--bg-secondary);
    }

    .social-links {
        justify-content: center;
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .social-link {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .social-link:active {
        transform: scale(0.95);
    }

    /* Navigation محسن للهواتف */
    .mobile-toggle {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
    }

    .mobile-toggle span {
        width: 22px;
        height: 2px;
        background: var(--text-primary);
        border-radius: 1px;
        transition: all 0.3s ease;
    }

    /* Touch optimizations */
    .cart-btn,
    .theme-toggle,
    .lang-btn {
        min-width: 48px;
        min-height: 48px;
        border-radius: 8px;
        touch-action: manipulation;
    }

    .add-to-cart {
        width: 100%;
        padding: 1rem;
        font-size: 1rem;
        font-weight: 600;
        border-radius: 12px;
        min-height: 48px;
    }
}

/* Extra Small Screens (360px and below) */
@media (max-width: 360px) {
    .hero-title {
        font-size: 1.75rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
    }

    .service-card,
    .product-card,
    .work-card {
        padding: 1.25rem;
    }

    .modal-content {
        width: 98%;
        margin: 0.5rem;
    }
}

/* Landscape Mobile Orientation */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
    }

    .hero-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .hero-image {
        order: 0;
    }

    .hero-stats {
        grid-template-columns: repeat(3, 1fr);
        max-width: none;
    }
}

/* Print Styles */
@media print {
    .header,
    .mobile-toggle,
    .nav-controls,
    .floating-card,
    .modal {
        display: none !important;
    }

    .hero {
        min-height: 60vh;
        padding: 2rem 0;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    section {
        padding: 1rem 0;
        break-inside: avoid;
    }

    .service-card,
    .product-card,
    .work-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .btn {
        display: none;
    }

    .footer {
        padding: 1rem 0;
    }

    .social-links {
        display: none;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-bg {
        background-size: 30px 30px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .hero-image img,
    .product-image img,
    .work-image img {
        transform: none !important;
    }

    .service-card:hover,
    .product-card:hover,
    .work-card:hover {
        transform: none !important;
    }
}

/* Dark Mode Specific Responsive Adjustments */
.dark-mode .nav-menu {
    background: var(--bg-primary);
    border-top-color: var(--border-color);
}

.dark-mode .floating-card {
    background: var(--bg-primary);
    box-shadow: 0 10px 25px var(--shadow-color);
}

/* Focus Styles for Accessibility */
@media (max-width: 768px) {
    .nav-link:focus,
    .btn:focus,
    .cart-btn:focus,
    .theme-toggle:focus,
    .lang-btn:focus {
        outline: 2px solid var(--primary-500);
        outline-offset: 2px;
    }

    .mobile-toggle:focus {
        outline: 2px solid var(--primary-500);
        outline-offset: 2px;
        border-radius: 4px;
    }
}

/* Touch Device Optimizations - تحسينات أجهزة اللمس */
@media (hover: none) and (pointer: coarse) {
    /* إزالة تأثيرات hover على أجهزة اللمس */
    .service-card:hover,
    .product-card:hover,
    .work-card:hover {
        transform: none;
    }

    .nav-link:hover::after {
        width: 0;
    }

    .nav-link.active::after {
        width: 100%;
    }

    /* زيادة أهداف اللمس للوصولية */
    .cart-btn,
    .theme-toggle,
    .lang-btn,
    .mobile-toggle {
        min-width: 44px;
        min-height: 44px;
        padding: 0.75rem;
    }

    .add-to-cart,
    .quantity-btn,
    .remove-btn {
        min-width: 44px;
        min-height: 44px;
        padding: 0.75rem;
    }

    /* تحسين تفاعل الأزرار */
    .btn:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    .service-card:active,
    .product-card:active,
    .work-card:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    /* تحسين النماذج للمس */
    .form-group input:focus,
    .form-group textarea:focus {
        transform: scale(1.02);
        transition: transform 0.2s ease;
    }
}

/* Tablet Specific Adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
    .hero-content {
        gap: 3rem;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .products-grid,
    .works-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .about-content,
    .contact-content {
        gap: 3rem;
    }
}

/* Large Mobile Screens (414px to 767px) */
@media (min-width: 414px) and (max-width: 767px) {
    .hero-title {
        font-size: 2.25rem;
    }

    .section-title {
        font-size: 2.25rem;
    }

    .service-card,
    .product-card,
    .work-card {
        max-width: 450px;
    }

    .hero-image img {
        height: 350px;
    }

    .product-image,
    .work-image {
        height: 200px;
    }
}
