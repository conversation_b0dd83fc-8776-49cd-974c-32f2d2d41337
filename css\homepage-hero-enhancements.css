/* ===================================
   تحسينات هيرو الصفحة الرئيسية
   Homepage Hero Enhancements
   =================================== */

/* ===== هيرو الصفحة الرئيسية المحسن ===== */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: calc(var(--header-height) + 2rem) 0 2rem 0;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="1.5"/></g></g></svg>');
    animation: float 20s ease-in-out infinite;
}

.hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 150px;
    background: linear-gradient(to top, var(--bg-primary), transparent);
    z-index: 1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(2deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 900px;
    margin: 0 auto;
    padding: 0 2rem;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-full);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.hero-title {
    font-size: clamp(2.5rem, 6vw, 5rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.02em;
}

.hero-title .highlight {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.hero-subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    line-height: 1.6;
    margin-bottom: 3rem;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 4rem;
}

.hero-btn {
    padding: 1.25rem 2.5rem;
    border-radius: var(--border-radius-lg);
    font-weight: 700;
    font-size: 1.1rem;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    border: 2px solid transparent;
}

.hero-btn.primary {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    color: #1e293b;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.hero-btn.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.hero-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.hero-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 4rem;
}

.hero-feature {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-xl);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.hero-feature:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
}

.hero-feature-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 1.5rem;
    color: white;
}

.hero-feature-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
}

.hero-feature-text {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
}

/* ===== تحسينات الهواتف المحمولة ===== */
@media (max-width: 768px) {
    .hero {
        min-height: 90vh;
        padding: calc(var(--header-height) + 1rem) 0 1rem 0;
    }
    
    .hero-content {
        padding: 0 1rem;
    }
    
    .hero-badge {
        font-size: 0.8rem;
        padding: 0.6rem 1.2rem;
        margin-bottom: 1.5rem;
    }
    
    .hero-title {
        font-size: clamp(2rem, 8vw, 3rem);
        margin-bottom: 1rem;
    }
    
    .hero-subtitle {
        font-size: clamp(1rem, 3vw, 1.2rem);
        margin-bottom: 2rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 3rem;
    }
    
    .hero-btn {
        width: 100%;
        max-width: 280px;
        padding: 1rem 2rem;
        font-size: 1rem;
    }
    
    .hero-features {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-top: 3rem;
    }
    
    .hero-feature {
        padding: 1.25rem;
        max-width: 300px;
        margin: 0 auto;
    }
    
    .hero-feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .hero-content {
        padding: 0 0.75rem;
    }
    
    .hero-title {
        font-size: clamp(1.75rem, 7vw, 2.5rem);
    }
    
    .hero-subtitle {
        font-size: clamp(0.9rem, 2.5vw, 1.1rem);
    }
    
    .hero-btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.95rem;
        min-width: 160px;
    }
    
    .hero-features {
        margin-top: 2rem;
    }
    
    .hero-feature {
        padding: 1rem;
    }
}

/* ===== تحسينات الأداء ===== */
@media (prefers-reduced-motion: reduce) {
    .hero::before,
    .hero-badge {
        animation: none;
    }
    
    .hero-btn:hover,
    .hero-feature:hover {
        transform: none;
    }
}

/* ===== تحسينات الثيم الداكن ===== */
[data-theme="dark"] .hero-btn.primary {
    background: linear-gradient(135deg, #1e293b, #334155);
    color: white;
}

[data-theme="dark"] .hero-btn.primary:hover {
    background: linear-gradient(135deg, #334155, #475569);
}
